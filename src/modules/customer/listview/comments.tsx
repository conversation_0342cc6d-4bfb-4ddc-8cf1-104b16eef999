/* eslint-disable react-native/no-inline-styles */
import {Dimensions, FlatList, Pressable, Text, View} from 'react-native';
import {DefaultComment} from '../../Default/card/defauttComment';
import {
  AppButton,
  FBottomSheet,
  FDialog,
  Rating,
  showBottomSheet,
} from 'wini-mobile-components';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import {useEffect, useRef} from 'react';
import WScreenFooter from '../../../Screen/Layout/footer';
import {TextFieldForm} from '../../Default/form/component-form';
import {useForm} from 'react-hook-form';
import {AppDispatch} from '../../../redux/store/store';
import {useDispatch} from 'react-redux';
import {RatingActions} from '../../../redux/reducers/ratingReducer';
import {useRatingData} from '../../../redux/hook/ratingHook';
import {getDataToAsyncStorage} from '../../../utils/AsyncStorage';
import {StorageContanst} from '../../../Config/Contanst';
import {navigate, RootScreen} from '../../../router/router';
import {dialogCheckAcc} from '../../../Screen/Layout/mainLayout';

export default function CommentsList(Id: any) {
  const bottomSheetRef = useRef<any>(null);
  const dialogRef = useRef<any>(null);
  const {ratings} = useRatingData(Id);
  const dispatch: AppDispatch = useDispatch();
  return (
    <View style={{flex: 1, width: '100%'}}>
      <FBottomSheet ref={bottomSheetRef} />
      <FDialog ref={dialogRef} />
      <FlatList
        nestedScrollEnabled
        scrollEnabled={false}
        // reverse list data
        data={ratings.listP}
        style={{width: '100%', height: '100%', flex: 1}}
        renderItem={({item}: any) => {
          // const listChildren: Array<any> = ratings.list.filter(
          //   (e: any) => e.ParentId === item.Id,
          // );
          return (
            <DefaultComment
              key={item.Id}
              onPressTitle={() => {
                navigate(RootScreen.ProfileCommunity, {Id: item.CustomerId});
              }}
              data={item}
              containerStyle={{padding: 16}}
              trailingView={<Rating value={item.Value} size={16} />}
              actionView={
                <View
                  style={{flex: 1, flexDirection: 'row', alignItems: 'center'}}>
                  <View
                    style={{
                      flex: 1,
                      flexDirection: 'row',
                      gap: 4,
                      justifyContent: 'flex-start',
                      alignItems: 'center',
                    }}>
                    <AppButton
                      backgroundColor={'transparent'}
                      borderColor="transparent"
                      onPress={async () => {
                        var token = await getDataToAsyncStorage(
                          StorageContanst.accessToken,
                        );
                        if (token) {
                          dispatch(
                            RatingActions.updateLike(
                              item.Id,
                              item.IsLike === true,
                            ),
                          );
                        } else {
                          ///TODO: check chưa login thì confirm ra trang login
                          dialogCheckAcc(dialogRef);
                        }
                      }}
                      containerStyle={{padding: 4}}
                      title={`${item.Likes}`}
                      textColor={
                        item.IsLike === false
                          ? ColorThemes.light.primary_main_color
                          : ColorThemes.light.neutral_text_subtitle_color
                      }
                      textStyle={TypoSkin.buttonText6}
                      prefixIconSize={16}
                      prefixIcon={
                        item.IsLike === false
                          ? 'fill/user interface/like'
                          : 'outline/user interface/like'
                      }
                    />
                    {/* <AppButton
                      backgroundColor={'transparent'}
                      borderColor="transparent"
                      onPress={() => {
                        showBottomSheet({
                          ref: bottomSheetRef,
                          title: 'Comment',
                          enableDismiss: true,
                          children: (
                            <CommentsView
                              parentId={item.Id}
                              courseId={item.CourseId}
                            />
                          ),
                        });
                      }}
                      containerStyle={{padding: 4}}
                      title={`${listChildren.length ?? 0}`}
                      textColor={ColorThemes.light.neutral_text_subtitle_color}
                      textStyle={TypoSkin.buttonText6}
                      prefixIconSize={16}
                      prefixIcon={'outline/user interface/b-comment'}
                    /> */}
                  </View>
                </View>
              }
            />
          );
        }}
      />
    </View>
  );
}

export const CommentsView = ({
  height,
  parentId,
  courseId,
}: {
  height?: any;
  parentId: any;
  courseId: any;
}) => {
  const methods = useForm<any>({
    shouldFocusError: false,
  });
  const dispatch: AppDispatch = useDispatch();
  const {ratings} = useRatingData(courseId);
  useEffect(() => {}, [dispatch]);
  return (
    <View
      style={{
        height: height ?? Dimensions.get('window').height / 1.66,
        width: '100%',
      }}>
      <Pressable style={{flex: 1, height: '100%'}}>
        <FlatList
          data={ratings.list.filter(item => item.ParentId === parentId)}
          style={{width: '100%', height: '100%'}}
          renderItem={({item}: any) => {
            return (
              <DefaultComment
                data={item}
                containerStyle={{padding: 16}}
                actionView={
                  <View
                    style={{
                      flex: 1,
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}>
                    <View
                      style={{
                        flex: 1,
                        flexDirection: 'row',
                        gap: 4,
                        justifyContent: 'flex-start',
                        alignItems: 'center',
                      }}>
                      <AppButton
                        backgroundColor={'transparent'}
                        borderColor="transparent"
                        onPress={async () => {
                          var token = await getDataToAsyncStorage(
                            StorageContanst.accessToken,
                          );
                          if (token) {
                            dispatch(
                              RatingActions.updateLike(
                                item.Id,
                                item.IsLike === true,
                              ),
                            );
                          } else {
                            ///TODO: check chưa login thì confirm ra trang login
                          }
                        }}
                        containerStyle={{padding: 4}}
                        title={`${item.Likes}`}
                        textColor={
                          item.IsLike === true
                            ? ColorThemes.light.neutral_text_subtitle_color
                            : ColorThemes.light.primary_main_color
                        }
                        textStyle={TypoSkin.buttonText6}
                        prefixIconSize={16}
                        prefixIcon={
                          item.IsLike === true
                            ? 'outline/user interface/like'
                            : 'fill/user interface/like'
                        }
                      />
                    </View>
                  </View>
                }
              />
            );
          }}
          ListEmptyComponent={() => {
            return (
              <View
                style={{
                  height: Dimensions.get('window').height / 1.66,
                  width: '100%',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}>
                <Text style={{...TypoSkin.heading7, textAlign: 'center'}}>
                  Chưa có bình luận nào
                </Text>
              </View>
            );
          }}
        />
      </Pressable>
      <WScreenFooter
        style={{
          flexDirection: 'row',
          gap: 8,
          paddingHorizontal: 16,
          paddingBottom: 16,
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
        <TextFieldForm
          textFieldStyle={{padding: 16}}
          style={{flex: 1}}
          register={methods.register}
          control={methods.control}
          errors={methods.formState.errors}
          placeholder="Write a comment"
          name="Comment"
        />
        <AppButton
          title={'Send'}
          backgroundColor={ColorThemes.light.primary_main_color}
          borderColor="transparent"
          containerStyle={{
            borderRadius: 8,
            paddingHorizontal: 12,
            height: 45,
          }}
          onPress={async () => {
            var token = await getDataToAsyncStorage(
              StorageContanst.accessToken,
            );
            if (token) {
              if (methods.getValues().Comment) {
                await dispatch(
                  RatingActions.addCommentRating(
                    parentId,
                    methods.getValues().Comment,
                    courseId,
                  ),
                );
                methods.reset();
              }
            } else {
              ///TODO: check chưa login thì confirm ra trang login
            }
          }}
          textColor={ColorThemes.light.neutral_absolute_background_color}
        />
      </WScreenFooter>
    </View>
  );
};
