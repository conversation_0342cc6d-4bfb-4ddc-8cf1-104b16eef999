import {
  Image,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import {ListTile} from 'wini-mobile-components';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import ConfigAPI from '../../../Config/ConfigAPI';

interface cardItem {
  Id: string;
  Name: string;
  Description: string;
  Price: number;
  Img: string;
  Content: string;
  relativeUser?: {image?: string; title?: string};
}

interface Props {
  containerStyle?: ViewStyle;
  flexDirection?: 'default' | 'row';
  imgStyle?: ViewStyle;
  titleStyle?: TextStyle;
  subtitleStyle?: TextStyle;
  data: cardItem;
  listItems?: Array<any>;
  listTags?: Array<any>;
  onPressSeeMore?: () => void;
  onPressDetail?: () => void;
  onPressLikeAction?: () => void;
  reportContent?: React.ReactNode;
  actionView?: React.ReactNode;
  horizontalList?: boolean;
}

export default function DefaultBanner(props: Props) {
  return (
    <TouchableOpacity
      onPress={props.onPressDetail}
      style={{
        borderTopLeftRadius: 8,
        borderRadius: 8,
        overflow: 'hidden',
        backgroundColor: ColorThemes.light.neutral_text_subtitle_color,
        ...props.containerStyle,
      }}>
      {/* background img */}
      <Image
        source={{
          uri: props?.data.Img
            ? `${ConfigAPI.urlImg + props?.data.Img}`
            : 'https://www.figma.com/file/QeG7fLsM5o0Oje9Wagi1xc/image/be9e79d2b2cc1e79b9b9d50cba88c6febddd5d7f',
        }}
        style={{
          ...props.imgStyle,
          position: 'absolute',
          width: '100%',
          height: '100%',
          objectFit: 'cover',
          overflow: 'hidden',
        }}
      />
      <ListTile
        style={{
          padding: 16,
          pointerEvents: 'none',
          backgroundColor: ColorThemes.light.transparent,
          justifyContent: 'flex-end',
          height: '100%',
          width: '100%',
          shadowColor: '#000000',
          shadowOffset: {
            width: 0,
            height: 3,
          },
          shadowRadius: 5,
          shadowOpacity: 1.0,
        }}
        title={props.data?.Name ?? '-'}
        titleStyle={[
          TypoSkin.heading7,
          {color: ColorThemes.light.neutral_text_stable_color},
        ]}
        subtitle={props.data?.Description ?? ''}
        subTitleStyle={[
          TypoSkin.subtitle4,
          {color: ColorThemes.light.neutral_text_stable_color},
        ]}
      />
    </TouchableOpacity>
  );
}
