import {ReactNode, useEffect, useMemo, useState} from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SettingDataController,
  TableController,
} from '../../../base/baseController';
import {
  KeyboardAvoidingView,
  ScrollView,
  Text,
  View,
  ViewStyle,
} from 'react-native';
import {useForm, UseFormReturn} from 'react-hook-form';
import {regexGetVariableByThis, RenderComponentByType} from './config';
import {randomGID, Ultis} from '../../../utils/Utils';
import {ComponentType, FEDataType} from './da';
import {BaseDA} from '../../../base/BaseDA';
import ConfigAPI from '../../../Config/ConfigAPI';
import {ColorThemes} from '../../../assets/skin/colors';
import {FLoading} from 'wini-mobile-components';

interface Props {
  id?: string;
  style?: ViewStyle;
  data?: {[p: string]: any};
  disabled?: boolean;
  readonly?: boolean;
  customFieldUI?: {[p: string]: (methods: UseFormReturn) => ReactNode};
  customOptions?: {[p: string]: Array<{[k: string]: any}>};
  onSubmit?: (ev: {[p: string]: any}) => void;
}

interface FormByTypeProps extends Props {
  formItem: {[p: string]: any};
}

function FormByType(props: FormByTypeProps) {
  const methodOptions = useForm({
    shouldFocusError: false,
    shouldUnregister: false,
  });

  const methods = useForm<any>({
    shouldFocusError: false,
    defaultValues: {Id: randomGID()},
    shouldUnregister: false,
  });
  const _colController = new TableController('column');
  const _relController = new TableController('rel');
  const [cols, setCols] = useState<Array<any>>([]);
  const [rels, setRels] = useState<Array<any>>([]);
  const watchRel = useMemo<Array<any>>(
    () =>
      rels.filter(
        e => e.Query && e.Query.match(regexGetVariableByThis)?.length,
      ),
    [rels.length],
  );
  const staticRel = useMemo<Array<any>>(
    () =>
      rels.filter(
        e => !e.Query?.length || !e.Query.match(regexGetVariableByThis)?.length,
      ),
    [rels.length],
  );
  useEffect(() => {
    if (props.formItem) {
      _relController
        .getListSimple({
          page: 1,
          size: 100,
          query: `@TableFK:{${props.formItem.TbName}} @Column:{${Object.keys(
            props.formItem.Props,
          )
            .filter(p => (props.formItem.Props as any)[p] >= 0)
            .join(' | ')}}`,
        })
        .then(res => {
          if (res.code === 200)
            setRels(
              res.data.map((e: any) => {
                let _tmp = {
                  ...e,
                  Form: e.Form
                    ? typeof e.Form === 'string'
                      ? JSON.parse(e.Form)
                      : {...e.Form}
                    : {Required: true},
                };
                _tmp.Form.Sort = (props.formItem.Props as any)[e.Column];
                _tmp.Form.Disabled = props.disabled;
                return _tmp;
              }),
            );
        });
      _colController
        .getListSimple({
          page: 1,
          size: 100,
          query: `@TableName:{${props.formItem.TbName}} @Name:{${Object.keys(
            props.formItem.Props,
          )
            .filter(p => (props.formItem.Props as any)[p] >= 0)
            .join(' | ')}}`,
        })
        .then(res => {
          if (res.code === 200) {
            setCols(
              res.data.map((e: any) => {
                let _tmp = {
                  ...e,
                  Form: e.Form
                    ? typeof e.Form === 'string'
                      ? JSON.parse(e.Form)
                      : {...e.Form}
                    : {Required: true},
                };
                _tmp.Form.Sort = (props.formItem.Props as any)[e.Name];
                _tmp.Form.Disabled = props.disabled;
                if (
                  props.readonly &&
                  _tmp.Form.ComponentType === ComponentType.select1
                )
                  _tmp.Form.ReadOnly = props.readonly;
                return _tmp;
              }),
            );
          }
        });
    }
  }, [props.formItem]);

  useEffect(() => {
    if (staticRel.length) getOptions({relatives: staticRel});
  }, [staticRel.length]);

  const getOptions = ({
    relatives = [],
    isWatch = false,
  }: {
    relatives?: Array<{[p: string]: any}>;
    page?: number;
    isWatch?: boolean;
  }) => {
    relatives.forEach(_rel => {
      let tmpOptions = undefined;
      if (props.customOptions) {
        tmpOptions = props.customOptions[`${_rel.Column}`];
      }
      if (tmpOptions)
        methodOptions.setValue(`${_rel.Column}_Options`, tmpOptions);
      else {
        const _dataPKController = new DataController(_rel.TablePK);
        if (_rel.TablePK === props.formItem.TbName) {
          _dataPKController
            .filterByEmptyKey({
              page: 1,
              size: 500,
              searchRaw: _rel.Query?.length
                ? isWatch
                  ? _rel.Query.replace(regexGetVariableByThis, (m: string) =>
                      methods.getValues(
                        (regexGetVariableByThis.exec(m) ?? [])[1],
                      ),
                    )
                  : _rel.Query
                : '*',
              key: `ParentId`,
            })
            .then(async res => {
              if (res.code === 200)
                methodOptions.setValue(
                  `${_rel.Column}_Options`,
                  res.data ?? [],
                );
            });
        } else {
          _dataPKController
            .getListSimple({
              page: 1,
              size: 1000,
              query: isWatch
                ? _rel.Query?.replace(regexGetVariableByThis, (m: string) =>
                    methods.getValues(
                      (regexGetVariableByThis.exec(m) ?? [])[1],
                    ),
                  )
                : _rel.Query,
              returns: ['Id', 'Name', 'ParentId'],
            })
            .then(res => {
              if (res.code === 200)
                methodOptions.setValue(
                  `${_rel.Column}_Options`,
                  res.data ?? [],
                );
            });
        }
      }
    });
  };

  useEffect(() => {
    if (cols.length) {
      methods.reset();
      if (props.formItem.Category === 1) {
        const mapData = (key: string, value: any) => {
          if (value === undefined || value === null) return;
          let _col = rels.find(e => e.Column === key);
          _col ??= cols.find(
            e => e.Name === key.replace('_min', '').replace('_max', ''),
          );
          if (_col) {
            switch (_col.DataType) {
              case FEDataType.NUMBER:
                methods.setValue(
                  key,
                  typeof value === 'string' ? parseFloat(value) : value,
                );
                break;
              case FEDataType.DATE:
              case FEDataType.DATETIME:
                methods.setValue(
                  key,
                  new Date(typeof value === 'string' ? parseInt(value) : value),
                );
                break;
              case FEDataType.MONEY:
                methods.setValue(key, Ultis.money(value));
                break;
              case FEDataType.BOOLEAN:
                if (_col.Form.ComponentType === ComponentType.radio)
                  methods.setValue(key, `${value}`);
                else methods.setValue(key, value === 'true');
                break;
              default:
                methods.setValue(
                  key,
                  _col.Form.ComponentType === ComponentType.selectMultiple
                    ? value.split(',')
                    : value,
                );
                break;
            }
          } else methods.setValue(key, value);
        };
        if (props.data) {
          for (const key in props.data) mapData(key, props.data[key]);
        }
        // for (const [key, value] of params) mapData(key, value)
      } else if (props.data) {
        const dataItem = props.data;
        const _fileIds: Array<any> = [];
        Object.keys(dataItem).forEach(prop => {
          const _col = cols.find(e => e.Name === prop);
          const _rel = rels.find(e => e.Column === prop);
          if (_col) {
            switch (_col.DataType) {
              case FEDataType.GID:
                methods.setValue(prop, dataItem[prop]);
                break;
              case FEDataType.STRING:
                if (_col.Form.Options?.length) {
                  methods.setValue(prop, (dataItem[prop] ?? '').split(','));
                } else {
                  methods.setValue(prop, dataItem[prop]);
                }
                break;
              case FEDataType.HTML:
                methods.setValue(prop, dataItem[prop]);
                break;
              case FEDataType.BOOLEAN:
                methods.setValue(prop, dataItem[prop]);
                if (_col.Form.ComponentType === ComponentType.radio)
                  methods.setValue(prop, `${dataItem[prop]}`);
                break;
              case FEDataType.NUMBER:
                methods.setValue(
                  prop,
                  typeof dataItem[prop] === 'string'
                    ? parseFloat(dataItem[prop])
                    : dataItem[prop],
                );
                break;
              case FEDataType.DATE:
              case FEDataType.DATETIME:
                methods.setValue(
                  prop,
                  new Date(
                    typeof dataItem[prop] === 'string'
                      ? parseInt(dataItem[prop])
                      : dataItem[prop],
                  ),
                );
                break;
              case FEDataType.MONEY:
                methods.setValue(prop, Ultis.money(dataItem[prop]));
                break;
              case FEDataType.PASSWORD:
                methods.setValue(prop, dataItem[prop]);
                break;
              case FEDataType.FILE:
                if (dataItem[prop])
                  _fileIds.push({
                    id: dataItem[prop],
                    name: prop,
                    multiple: _col.Form.Multiple,
                  });
                break;
              default:
                break;
            }
          } else if (_rel) {
            const _tmpParse = dataItem[prop]?.length
              ? dataItem[prop].split(',')
              : [];
            methods.setValue(
              prop,
              _rel.Form.ComponentType === ComponentType.selectMultiple
                ? _tmpParse
                : _tmpParse[0],
            );
          } else {
            methods.setValue(prop, dataItem[prop]);
          }
        });
        if (_fileIds.length) {
          BaseDA.getFilesInfor(
            _fileIds.map(e => e.id.split(',')).flat(Infinity),
          ).then(res => {
            if (res.code === 200)
              _fileIds.forEach(e => {
                const _file = res.data.filter(
                  (_file: any) =>
                    _file !== undefined &&
                    _file !== null &&
                    e.id.includes(_file.Id),
                );
                if (_file.length)
                  methods.setValue(
                    e.name,
                    _file.map((f: any) => ({
                      ...f,
                      name: f.Name,
                      size: f.Size,
                      type: f.Type,
                      url: ConfigAPI.urlImg + f.Id,
                    })),
                  );
              });
          });
        }
      } else {
        cols
          .filter(e => e.DefaultValue != undefined)
          .forEach(_col => {
            switch (_col.DataType) {
              case FEDataType.GID:
                methods.setValue(_col.Name, _col.Form.DefaultValue);
                break;
              case FEDataType.STRING:
                if (_col.Form.Options?.length) {
                  methods.setValue(
                    _col.Name,
                    _col.Form.DefaultValue.split(','),
                  );
                } else {
                  methods.setValue(_col.Name, _col.Form.DefaultValue);
                }
                break;
              case FEDataType.HTML:
                methods.setValue(_col.Name, _col.Form.DefaultValue);
                break;
              case FEDataType.BOOLEAN:
                methods.setValue(_col.Name, _col.Form.DefaultValue);
                break;
              case FEDataType.NUMBER:
                methods.setValue(
                  _col.Name,
                  typeof _col.Form.DefaultValue === 'string'
                    ? parseFloat(_col.Form.DefaultValue)
                    : _col.Form.DefaultValue,
                );
                break;
              case FEDataType.DATE:
              case FEDataType.DATETIME:
                methods.setValue(
                  _col.Name,
                  new Date(
                    typeof _col.DefaultValue === 'string'
                      ? parseInt(_col.DefaultValue)
                      : _col.DefaultValue,
                  ),
                );
                break;
              case FEDataType.MONEY:
                methods.setValue(
                  _col.Name,
                  Ultis.money(_col.Form.DefaultValue),
                );
                break;
              case FEDataType.PASSWORD:
                methods.setValue(_col.Name, _col.Form.DefaultValue);
                break;
              default:
                break;
            }
          });
      }
    }
  }, [props.data, cols.length]);

  return cols
    .filter(e => e.Name !== 'Id' && e.Name !== 'DateCreated')
    .map((_col: any) => {
      if (props.customFieldUI?.[_col.Name])
        return props.customFieldUI?.[_col.Name](methods);
      return (
        <RenderComponentByType
          key={_col.Id}
          methods={methods}
          fieldItem={_col}
          label={undefined}
          style={{order: _col.Form.Sort}}
          labelStyle={
            props.formItem.Type === 'left-label col' ? {width: 160} : undefined
          }
        />
      );
    });
}

interface FormByIdProps extends Props {
  formId: string;
}

export default function FormById(props: FormByIdProps) {
  const [formItem, setFormItem] = useState<{[p: string]: any}>();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (props.formId) {
      setLoading(true);
      const _settingDataController = new SettingDataController('form');
      _settingDataController.getByIds([props.formId]).then(async res => {
        if (res.code === 200) {
          setLoading(false);
          let _formItem = res.data[0];
          if (typeof _formItem.Props === 'string')
            _formItem.Props = JSON.parse(_formItem.Props);
          setFormItem(_formItem);
        }
      });
    } else if (formItem) setFormItem(undefined);
    setLoading(false);
  }, [props.formId]);

  return (
    <KeyboardAvoidingView
      style={{
        flex: 1,
        paddingHorizontal: 16,
      }}>
      <FLoading visible={loading} />
      {formItem ? (
        <ScrollView style={{flex: 1}}>
          <View style={{gap: 16}}>
            <FormByType key={formItem.Id} formItem={formItem} {...props} />
          </View>
        </ScrollView>
      ) : null}
    </KeyboardAvoidingView>
  );
}
