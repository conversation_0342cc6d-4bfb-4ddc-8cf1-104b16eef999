import React, { useEffect } from 'react';
import {View, Text, FlatList} from 'react-native';


export default function ListHorizontal({data }: any) {
  useEffect(()=>{
//call api
  });
  // const data = [
  //   {id: 1, name: 'Item 1'},
  //   {id: 2, name: 'Item 2'},
  //   {id: 3, name: 'Item 3'},
  //   {id: 4, name: 'Item 4'},
  //   {id: 5, name: 'Item 5'},
  //   {id: 6, name: 'Item 5'},
  //   {id: 7, name: 'Item 5'},
  //   {id: 8, name: 'Item 5'},
  // ];

  const renderItem = ({item, index}: {item: any; index: number}) => (
    <View style={{padding: 10, backgroundColor: '#fafafa'}}>
      <Text>{item.name} {index}</Text>
    </View>
  );

  return (
    <View style={{flex: 1, width: '100%'}}>
      <FlatList
        data={data}
        renderItem={({item, index}) => renderItem({item, index})}
        style={{width: '100%', height: '100%'}}
        keyExtractor={item => item.id.toString()}
        horizontal
      />
    </View>
  );
}
