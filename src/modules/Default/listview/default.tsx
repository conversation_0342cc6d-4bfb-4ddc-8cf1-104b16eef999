import React, {useEffect, useState} from 'react';
import {View, Text, FlatList, RefreshControl} from 'react-native';
import {DefaultWithImage, SkeletonPlaceCard} from '../card/defaultImage';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import {AppButton} from 'wini-mobile-components';
import DefaultBanner from '../card/infor';

interface Props {
  horizontal?: boolean;
  titleList?: string;
  isSeeMore?: boolean;
}

const data1 = [
  {
    Id: 1,
    Name: 'Item 1',
    // Img: 'https://www.figma.com/file/QeG7fLsM5o0Oje9Wagi1xc/image/be9e79d2b2cc1e79b9b9d50cba88c6febddd5d7f',
    Description: 'Description 1',
    Price: 100,
    Rate: 4,
  },
  {
    Id: 2,
    Name: 'Item 1',
    Img: 'https://www.figma.com/file/QeG7fLsM5o0Oje9Wagi1xc/image/be9e79d2b2cc1e79b9b9d50cba88c6febddd5d7f',
    Description: 'Description 1',
    Price: 100,
    Rate: 4,
  },
  {
    Id: 3,
    Name: 'Item 1',
    Img: 'https://www.figma.com/file/QeG7fLsM5o0Oje9Wagi1xc/image/be9e79d2b2cc1e79b9b9d50cba88c6febddd5d7f',
    Description: 'Description 1',
    Price: 100,
    Rate: 4,
  },
];

export default function DefaultList(props: Props) {
  const [data, setData] = useState<Array<any>>([]);

  const [isLoading, setLoading] = useState(false);

  useEffect(() => {
    onRefresh();
  }, []);

  const onRefresh = () => {
    setLoading(true);
    setData([]);
    setTimeout(() => {
      setData(data1);
      setLoading(false);
    }, 2000);
  };

  return (
    <View
      style={{
        height: props.horizontal ? 180 : undefined,
      }}>
      {props.titleList ? (
        <View
          style={{
            alignItems: 'center',
            justifyContent: 'space-between',
            paddingHorizontal: 16,
            flexDirection: 'row',
            backgroundColor:
              ColorThemes.light.neutral_absolute_background_color,
          }}>
          <Text
            style={{
              ...TypoSkin.heading5,
              color: ColorThemes.light.neutral_text_title_color,
            }}>
            {props.titleList}
          </Text>
          {props.isSeeMore ? (
            <AppButton
              title={'See more'}
              containerStyle={{
                justifyContent: 'flex-start',
                alignSelf: 'baseline',
              }}
              backgroundColor={'transparent'}
              textStyle={TypoSkin.buttonText3}
              borderColor="transparent"
              suffixIconSize={16}
              suffixIcon={'outline/arrows/circle-arrow-right'}
              onPress={() => {}}
              textColor={ColorThemes.light.infor_main_color}
            />
          ) : null}
        </View>
      ) : null}
      <FlatList
        data={data}
        nestedScrollEnabled
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isLoading}
            onRefresh={() => {
              onRefresh();
            }}
          />
        }
        contentContainerStyle={{
          gap: 16,
          backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        }}
        renderItem={({item, index}) => {
          return (
            <DefaultBanner
              key={index}
              flexDirection="default"
              containerStyle={{width: 180, height: 180}}
              data={item}
            />
          );
        }}
        style={{width: '100%', height: '100%', paddingHorizontal: 16}}
        keyExtractor={item => item.Id.toString()}
        horizontal={props.horizontal}
        ListEmptyComponent={() => {
          if (isLoading) {
            return <SkeletonPlaceCard />;
          }
          return <Text style={{color: '#000000'}}>Không có dữ liệu</Text>;
        }}
      />
    </View>
  );
}
