import React, {useState, useEffect, useCallback, useRef} from 'react';
import {
  View,
  Text,
  FlatList,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  StyleSheet,
  Dimensions,
  SafeAreaView,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {useTranslation} from 'react-i18next';
import {ProductDA, ProductItem} from '../productDA';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import {RootScreen} from '../../../router/router';
import EmptyPage from '../../../Screen/emptyPage';
import ProductCard from '../card/ProductCard';
import ScreenHeader from '../../../Screen/Layout/header';
import {Winicon} from 'wini-mobile-components';

const {width} = Dimensions.get('window');
const ITEM_WIDTH = (width - 48) / 2;
const ITEM_HEIGHT = ITEM_WIDTH * 1.8;

interface SearchIndexProps {
  ref?: React.RefObject<any>;
}

const SearchIndex: React.FC<SearchIndexProps> = React.forwardRef(
  (props, ref) => {
    const navigation = useNavigation<any>();
    const {t} = useTranslation();
    const productDA = new ProductDA();
    const searchInputRef = useRef<TextInput>(null);

    // States
    const [searchValue, setSearchValue] = useState<string>('');
    const [products, setProducts] = useState<ProductItem[]>([]);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [isRefreshing, setIsRefreshing] = useState<boolean>(false);
    const [isLoadingMore, setIsLoadingMore] = useState<boolean>(false);
    const [page, setPage] = useState<number>(1);
    const [hasMoreData, setHasMoreData] = useState<boolean>(true);
    const [error, setError] = useState<string>('');

    // Search function
    const searchProducts = useCallback(
      async (
        searchText: string,
        pageNumber: number = 1,
        isRefresh: boolean = false,
      ) => {
        if (!searchText.trim()) {
          setProducts([]);
          return;
        }

        try {
          if (pageNumber === 1) {
            isRefresh ? setIsRefreshing(true) : setIsLoading(true);
          } else {
            setIsLoadingMore(true);
          }

          setError('');

          // Create search query for product name
          const query = `@Name: (*${searchText.trim()}*)`;

          const result = await productDA.getAllList(pageNumber, 20, query, [
            'Id',
            'Name',
            'Price',
            'Img',
            'Discount',
            'CustomerId',
          ]);

          if (result && result.code === 200) {
            const newProducts = result.data || [];

            if (pageNumber === 1) {
              setProducts(newProducts);
            } else {
              setProducts(prev => [...prev, ...newProducts]);
            }

            // Check if there's more data
            setHasMoreData(newProducts.length === 20);
          } else {
            if (pageNumber === 1) {
              setProducts([]);
            }
            setError('Không thể tải dữ liệu sản phẩm');
          }
        } catch (err) {
          console.error('Error searching products:', err);
          setError('Đã xảy ra lỗi khi tìm kiếm');
          if (pageNumber === 1) {
            setProducts([]);
          }
        } finally {
          setIsLoading(false);
          setIsRefreshing(false);
          setIsLoadingMore(false);
        }
      },
      [productDA],
    );

    // Handle search input change
    const handleSearchChange = useCallback((text: string) => {
      setSearchValue(text);
      setPage(1);

      if (text.trim().length === 0) {
        setProducts([]);
        setError('');
        return;
      }
    }, []);

    // Debounced search effect
    useEffect(() => {
      if (searchValue.trim().length === 0) {
        return;
      }

      const timeoutId = setTimeout(() => {
        searchProducts(searchValue, 1);
      }, 500);

      return () => clearTimeout(timeoutId);
    }, [searchValue, searchProducts]);

    // Clear search
    const clearSearch = useCallback(() => {
      setSearchValue('');
      setProducts([]);
      setError('');
      setPage(1);
      searchInputRef.current?.focus();
    }, []);

    // Handle refresh
    const handleRefresh = useCallback(() => {
      if (searchValue.trim()) {
        setPage(1);
        searchProducts(searchValue, 1, true);
      }
    }, [searchValue, searchProducts]);

    // Handle load more
    const handleLoadMore = useCallback(() => {
      if (
        !isLoadingMore &&
        hasMoreData &&
        searchValue.trim() &&
        products.length > 0
      ) {
        const nextPage = page + 1;
        setPage(nextPage);
        searchProducts(searchValue, nextPage);
      }
    }, [
      isLoadingMore,
      hasMoreData,
      searchValue,
      products.length,
      page,
      searchProducts,
    ]);

    // Handle product press
    const handleProductPress = useCallback(
      (product: ProductItem) => {
        navigation.navigate(RootScreen.ProductDetail, {id: product.Id});
      },
      [navigation],
    );

    // Render product item
    const renderProductItem = useCallback(
      ({item}: {item: ProductItem}) => (
        <View style={styles.productItemContainer}>
          <ProductCard
            item={item}
            onPress={handleProductPress}
            width={ITEM_WIDTH}
            height={ITEM_HEIGHT}
          />
        </View>
      ),
      [handleProductPress],
    );

    // Render footer loading
    const renderFooter = useCallback(() => {
      if (!isLoadingMore) return null;

      return (
        <View style={styles.footerContainer}>
          <ActivityIndicator
            size="small"
            color={ColorThemes.light.primary_main_color}
          />
          <Text style={styles.loadingText}>Đang tải thêm...</Text>
        </View>
      );
    }, [isLoadingMore]);

    // Render empty component
    const renderEmpty = useCallback(() => {
      if (isLoading) return null;

      if (!searchValue.trim()) {
        return (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyTitle}>Tìm kiếm sản phẩm</Text>
            <Text style={styles.emptySubtitle}>
              Nhập tên sản phẩm để bắt đầu tìm kiếm
            </Text>
          </View>
        );
      }

      if (error) {
        return <EmptyPage title="Đã xảy ra lỗi" subtitle={error} />;
      }

      return (
        <EmptyPage
          title="Không tìm thấy sản phẩm"
          subtitle={`Không có sản phẩm nào phù hợp với "${searchValue}"`}
        />
      );
    }, [isLoading, searchValue, error]);

    return (
      <SafeAreaView style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}>
            <Winicon
              src="outline/arrows/arrow-left"
              size={24}
              color={ColorThemes.light.neutral_text_title_color}
            />
          </TouchableOpacity>

          <View style={styles.searchContainer}>
            <TextInput
              ref={searchInputRef}
              style={styles.searchInput}
              placeholder="Tìm kiếm sản phẩm..."
              placeholderTextColor={
                ColorThemes.light.neutral_text_subtitle_color
              }
              value={searchValue}
              onChangeText={handleSearchChange}
              autoFocus={true}
              returnKeyType="search"
              onSubmitEditing={() => {
                if (searchValue.trim()) {
                  searchProducts(searchValue, 1);
                }
              }}
            />

            {searchValue.length > 0 && (
              <TouchableOpacity
                style={styles.clearButton}
                onPress={clearSearch}>
                <Winicon
                  src="outline/interface/close"
                  size={20}
                  color={ColorThemes.light.neutral_text_subtitle_color}
                />
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* Search Results */}
        <View style={styles.content}>
          {isLoading && products.length === 0 ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator
                size="large"
                color={ColorThemes.light.primary_main_color}
              />
              <Text style={styles.loadingText}>Đang tìm kiếm...</Text>
            </View>
          ) : (
            <FlatList
              data={products}
              renderItem={renderProductItem}
              keyExtractor={(item, index) => item.Id || index.toString()}
              numColumns={2}
              contentContainerStyle={styles.listContent}
              showsVerticalScrollIndicator={false}
              refreshControl={
                <RefreshControl
                  refreshing={isRefreshing}
                  onRefresh={handleRefresh}
                  colors={[ColorThemes.light.primary_main_color]}
                  tintColor={ColorThemes.light.primary_main_color}
                />
              }
              onEndReached={handleLoadMore}
              onEndReachedThreshold={0.5}
              ListFooterComponent={renderFooter}
              ListEmptyComponent={renderEmpty}
              columnWrapperStyle={
                products.length > 0 ? styles.columnWrapper : undefined
              }
            />
          )}
        </View>
      </SafeAreaView>
    );
  },
);

SearchIndex.displayName = 'SearchIndex';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.neutral_border_color,
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ColorThemes.light.neutral_surface_color,
    borderRadius: 8,
    paddingHorizontal: 12,
    height: 44,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: ColorThemes.light.neutral_text_title_color,
    paddingVertical: 0,
    ...TypoSkin.body2,
  },
  clearButton: {
    padding: 4,
    marginLeft: 8,
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 14,
    color: ColorThemes.light.neutral_text_subtitle_color,
    ...TypoSkin.body3,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingVertical: 40,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: ColorThemes.light.neutral_text_title_color,
    textAlign: 'center',
    marginBottom: 8,
    ...TypoSkin.heading4,
  },
  emptySubtitle: {
    fontSize: 14,
    color: ColorThemes.light.neutral_text_subtitle_color,
    textAlign: 'center',
    lineHeight: 20,
    ...TypoSkin.body3,
  },
  listContent: {
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  productItemContainer: {
    flex: 1,
    marginHorizontal: 4,
    marginBottom: 16,
  },
  columnWrapper: {
    justifyContent: 'space-between',
    paddingHorizontal: 4,
  },
  footerContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 16,
    gap: 8,
  },
});

export default SearchIndex;
