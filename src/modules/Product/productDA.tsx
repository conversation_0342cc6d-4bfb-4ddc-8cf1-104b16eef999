import {DataController} from '../../base/baseController';
import {StorageContanst} from '../../Config/Contanst';
import {getDataToAsyncStorage} from '../../utils/AsyncStorage';

export interface ProductItem {
  Id?: string;
  Name?: string;
  Img?: string;
  Price: number;
  IsCare: boolean;
  Description?: string;
  Status?: number;
  // originalPrice?: number;
  Discount?: number;
  rating: number;
  soldCount: number;
}

export class ProductDA {
  private ProductController: DataController;
  private orderDetailController: DataController;
  private wishlishController: DataController;

  constructor(
    ProductController = new DataController('Product'),
    wishlishController = new DataController('Wishlish'),
    orderDetailController = new DataController('OrderDetail'),
  ) {
    this.ProductController = ProductController;
    this.orderDetailController = orderDetailController;
    this.wishlishController = wishlishController;
  }

  async getProductPopular(pageSize: number) {
    const responseProduct = await this.orderDetailController.group({
      searchRaw: '*',
      reducers: 'LOAD * GROUPBY 1 @ProductId REDUCE COUNT 0 AS CountProduct',
    });

    if (responseProduct.code === 200) {
      var lstProduct = responseProduct.data;
      //sắp xếp lại khóa học sau khi đã count trong đơn hàng để lấy khóa đc mua nhiều nhất. và lấy top 10
      if (lstProduct.length > 0) {
        if (pageSize > 0) {
          lstProduct = [...lstProduct]
            .sort(
              (a, b) =>
                parseInt(a.CountProduct, 10) - parseInt(b.CountProduct, 10),
            )
            .slice(0, pageSize);
        } else {
          lstProduct = [...lstProduct].sort(
            (a, b) =>
              parseInt(a.CountProduct, 10) - parseInt(b.CountProduct, 10),
          );
        }

        const respone = await this.ProductController.getListSimple({
          page: 1,
          size: 50,
          query: `@Id: {${lstProduct
            .map((item: any) => item.ProductId)
            .join(' | ')}}`,
          returns: ['Id', 'Name', 'Price', 'Img'],
          sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
        });

        if (respone.code === 200) {
          return respone;
        }
      } else {
        const respone = await this.ProductController.getListSimple({
          page: 1,
          size: 20,
          query: '@IsHot: {true}',
          returns: ['Id', 'Name', 'Price', 'Img'],
          sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
        });

        if (respone.code === 200) {
          return respone;
        }
      }
    }
    return null;
  }
  //lấy danh sach sản phẩm hot
  async getProductHot(page?: number, size?: number) {
    const respone = await this.ProductController.getPatternList({
      page: page,
      size: size,
      query: `@IsHot: {true}`,
      returns: ['Id', 'Name', 'Price', 'Img', 'Discount', 'ShopId'],
      pattern: {
        ShopId: ['Id', 'Name', 'Avatar', 'Address', 'Mobile', 'Email'],
      },
    });
    if (respone.code === 200) {
      return respone;
    }
    return null;
  }
  //lấy danh sách sản phẩm bán chạy
  async getProductBestSeller(page?: number, size?: number) {
    const responseProduct = await this.orderDetailController.group({
      searchRaw: '*',
      reducers: 'LOAD * GROUPBY 1 @ProductId REDUCE COUNT 0 AS CountProduct',
    });

    if (responseProduct.code === 200) {
      var lstProduct = responseProduct.data;
      //sắp xếp lại sản phẩm sau khi đã count trong đơn hàng để lấy  đc mua nhiều nhất. và lấy top 10
      if (lstProduct.length > 0) {
        if (size && size > 0) {
          lstProduct = [...lstProduct]
            .sort(
              (a, b) =>
                parseInt(a.CountProduct, 10) - parseInt(b.CountProduct, 10),
            )
            .slice(0, size);
        } else {
          lstProduct = [...lstProduct].sort(
            (a, b) =>
              parseInt(a.CountProduct, 10) - parseInt(b.CountProduct, 10),
          );
        }

        const respone = await this.ProductController.getPatternList({
          page: 1,
          size: 50,
          query: `@Id: {${lstProduct
            .map((item: any) => item.ProductId)
            .join(' | ')}}`,
          pattern: {
            ShopId: ['Id', 'Name', 'Avatar', 'Address', 'Mobile', 'Email'],
          },
          returns: ['Id', 'Name', 'Price', 'Img', 'Discount', 'ShopId'],
          sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
        });

        if (respone.code === 200) {
          return respone;
        }
      }
    }
    return null;
  }

  async getAllList(
    page: number,
    size: number,
    query: string,
    returns?: Array<string>,
  ) {
    const respone = await this.ProductController.getListSimple({
      page: page,
      size: size,
      query: query ?? '*',
      returns: returns,
      sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
    });
    if (respone.code === 200) {
      return respone;
    }
    return null;
  }

  async getAllListbyCategory(page?: any, size?: any, cateId?: string) {
    const respone = await this.ProductController.getListSimple({
      page: page,
      size: size,
      query: `@CategoryId:{${cateId}}`,
      returns: ['Id', 'Name', 'Price', 'Img', 'CustomerId'],
      sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
    });
    if (respone.code === 200) {
      return respone;
    }
    return null;
  }
  async getAllListbyCustomerId(page: number, size: number, customerId: string) {
    const respone = await this.ProductController.getListSimple({
      page: page,
      size: size,
      query: `@CustomerId:{${customerId}}`,
      sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
    });
    if (respone.code === 200) {
      return respone;
    }
    return null;
  }
  async getProductDetail(ProductId: string) {
    const respone = await this.ProductController.getPatternList({
      query: `@Id:{${ProductId}}`,
      pattern: {
        ShopId: ['Id', 'Name', 'Address', 'Mobile', 'Email', 'CustomerId'],
      },
    });
    if (respone.code === 200) {
      return respone;
    }
    return null;
  }
  //check sản phẩm có trong giỏ hàng hay không
  async checkProductIsWishlishCustomer(ProductIds: string[]) {
    const cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (cusId) {
      const wishlishResult = await this.wishlishController.getListSimple({
        query: `@CustomerId: {${cusId}} @ProductId:{${ProductIds.join(' | ')}}`,
        returns: ['ProductId'],
      });
      if (wishlishResult.code === 200 && wishlishResult.data?.length > 0) {
        return wishlishResult.data;
      }
    }
    return false;
  }
}
