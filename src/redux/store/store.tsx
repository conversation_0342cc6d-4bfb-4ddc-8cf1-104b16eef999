import {configureStore} from '@reduxjs/toolkit';
// import customerReducer from '../reducers/user/reducer'
import locationReducer from '../location-reducer';
import customerReducer from '../reducers/CustomerReducer';
import notificationReducer from '../reducers/notificationReducer';
import cartReducer from '../reducers/CartReducer';

export const store = configureStore({
  reducer: {
    location: locationReducer,
    customer: customerReducer,
    notification: notificationReducer,
    cart: cartReducer,
  },
});

export default store;
export type AppDispatch = typeof store.dispatch;
export type RootState = ReturnType<typeof store.getState>;
