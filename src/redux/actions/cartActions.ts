import { Dispatch } from 'redux';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AddToCartAction, CartActionTypes, CartItem, ClearCartAction, RemoveFromCartAction, SelectStoreItemsAction, ToggleSelectItemAction, UpdateQuantityAction } from '../types/cartTypes';


// Key để lưu giỏ hàng vào AsyncStorage
const CART_STORAGE_KEY = '@chainivo_cart';

// Action creators
export const addToCart = (item: CartItem): AddToCartAction => ({
  type: CartActionTypes.ADD_TO_CART,
  payload: item,
});

export const removeFromCart = (id: string): RemoveFromCartAction => ({
  type: CartActionTypes.REMOVE_FROM_CART,
  payload: id,
});

export const updateQuantity = (id: string, quantity: number): UpdateQuantityAction => ({
  type: CartActionTypes.UPDATE_QUANTITY,
  payload: { id, quantity },
});

export const toggleSelectItem = (id: string): ToggleSelectItemAction=> ({
  type: CartActionTypes.TOGGLE_SELECT_ITEM,
  payload: id,
});

export const selectStoreItems = (storeId: string, selected: boolean): SelectStoreItemsAction => ({
  type: CartActionTypes.SELECT_STORE_ITEMS,
  payload: { storeId, selected },
});

export const clearCart = (): ClearCartAction => ({
  type: CartActionTypes.CLEAR_CART,
});

// Async actions with Thunk
export const loadCart = () => async (dispatch: Dispatch) => {
  dispatch({ type: CartActionTypes.LOAD_CART });

  try {
    // Lấy dữ liệu giỏ hàng từ AsyncStorage
    const cartData = await AsyncStorage.getItem(CART_STORAGE_KEY);
    
    if (cartData) {
      const cartItems: CartItem[] = JSON.parse(cartData);
      dispatch({
        type: CartActionTypes.LOAD_CART_SUCCESS,
        payload: cartItems,
      });
    } else {
      // Nếu không có dữ liệu, khởi tạo giỏ hàng trống
      dispatch({
        type: CartActionTypes.LOAD_CART_SUCCESS,
        payload: [],
      });
    }
  } catch (error) {
    dispatch({
      type: CartActionTypes.LOAD_CART_FAILURE,
      payload: error instanceof Error ? error.message : 'Failed to load cart',
    });
  }
};

// Hàm helper để lưu giỏ hàng vào AsyncStorage
export const saveCartToStorage = async (items: CartItem[]) => {
  try {
    await AsyncStorage.setItem(CART_STORAGE_KEY, JSON.stringify(items));
  } catch (error) {
    console.error('Error saving cart to storage:', error);
  }
};

// Thunk middleware để lưu giỏ hàng sau mỗi thay đổi
export const saveCart = (items: CartItem[]) => async () => {
  await saveCartToStorage(items);
};
