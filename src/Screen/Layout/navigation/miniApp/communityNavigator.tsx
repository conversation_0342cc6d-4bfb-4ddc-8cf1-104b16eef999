import React from 'react';

const CommunityNavigation: React.FC = () => {
  return <></>;
  // const Stack = createNativeStackNavigator();
  // return (
  //   <Stack.Navigator screenOptions={{headerShown: false}}>
  //     <Stack.Screen
  //       name={RootScreen.CommunityLayout}
  //       component={CommunityLayout}
  //     />
  //     {/* groups */}
  //     <Stack.Screen
  //       name={RootScreen.GroupIndex}
  //       component={GroupIndex}
  //       // options={{
  //       //   animation: 'slide_from_bottom',
  //       //   animationDuration: 250,
  //       // }}
  //     />
  //     <Stack.Screen
  //       name={RootScreen.AllGroupsLoadMore}
  //       component={AllGroupsLoadMore}
  //     />
  //     <Stack.Screen name={RootScreen.PostDetail} component={PostDetail} />
  //     <Stack.Screen
  //       name={RootScreen.ProfileCommunity}
  //       component={ProfileCommunity}
  //     />
  //     <Stack.Screen
  //       name={RootScreen.NotifCommunity}
  //       component={NotifCommunity}
  //     />
  //     <Stack.Screen
  //       name={RootScreen.createPost}
  //       component={CreatePost}
  //       options={{
  //         animation: 'slide_from_bottom',
  //         animationDuration: 250,
  //       }}
  //     />
  //   </Stack.Navigator>
  // );
};

export default CommunityNavigation;
