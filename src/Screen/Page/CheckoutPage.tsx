import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  StatusBar,
  Platform,
} from 'react-native';
import {useNavigation, useRoute} from '@react-navigation/native';
import {
  Winicon,
  ComponentStatus,
  showSnackbar,
  AppSvg,
} from 'wini-mobile-components';
import {TypoSkin} from '../../assets/skin/typography';
import Svg, {Path} from 'react-native-svg';
import FastImage from 'react-native-fast-image';
import ConfigAPI from '../../Config/ConfigAPI';
import {CartItem} from '../../redux/types/cartTypes';
import {randomGID, Ultis} from '../../utils/Utils';
import iconSvg from '../../svg/icon';
import {OrderDA} from '../../modules/order/orderDA';
import {useSelectorCustomerState} from '../../redux/hook/customerHook';
import {useCartActions} from '../../redux/hook/cartHook';
import {StatusOrder} from '../../Config/Contanst';
import {RootScreen} from '../../router/router';
import {RenderHeaderCart, RenderRecipientInfo} from './CartPage';
import {SafeAreaView} from 'react-native-safe-area-context';

const {width} = Dimensions.get('window');
const STATUSBAR_HEIGHT = StatusBar.currentHeight || 0;

interface CheckoutRouteParams {
  items: CartItem[];
  address: any;
}

interface StoreGroup {
  ShopId: string;
  ShopName: string;
  ShopAvatar: string;
  items: CartItem[];
  totalPrice: number;
}

const CheckoutPage: React.FC = () => {
  const navigation = useNavigation<any>();
  const route = useRoute();
  const cartActions = useCartActions();
  const [isProcessing, setIsProcessing] = useState(false);
  const [_orderSuccess, setOrderSuccess] = useState(false);
  const orderDA = new OrderDA();
  // Lấy dữ liệu từ route params
  const {items = [], address = null} = (route.params as CheckoutRouteParams) || {};
  const customer = useSelectorCustomerState().data;

  // Nhóm các sản phẩm theo cửa hàng
  const storeGroups: StoreGroup[] = React.useMemo(() => {
    const storeMap = new Map<string, StoreGroup>();

    items.forEach(item => {
      if (!storeMap.has(item.ShopId)) {
        storeMap.set(item.ShopId, {
          ShopId: item.ShopId,
          ShopName: item.ShopName,
          ShopAvatar: item.ShopAvatar,
          items: [],
          totalPrice: 0,
        });
      }
      const group = storeMap.get(item.ShopId)!;
      group.items.push(item);
      group.totalPrice += item.Price * item.Quantity;
    });

    return Array.from(storeMap.values());
  }, [items]);

  // Tính tổng tiền của tất cả sản phẩm
  const totalPrice = React.useMemo(() => {
    return storeGroups.reduce((sum, group) => sum + group.totalPrice, 0);
  }, [storeGroups]);

  // Xử lý khi nhấn nút đặt hàng
  const handlePlaceOrder = async () => {
    if (isProcessing) {
      return;
    }
    setIsProcessing(true);

    try {
      if (storeGroups.length > 0) {
        // Lưu danh sách ID của các sản phẩm đã đặt hàng thành công
        var successfullyOrderedItemIds: any[] = [];
        var listOrder: any[] = [];
        var listOrderDetail: any[] = [];
        for (const group of storeGroups) {
          const order = {
            Id: randomGID(),
            CustomerId: customer?.Id,
            Name: customer?.Name,
            ShopId: group.ShopId,
            Code: Ultis.randomString(10).toLocaleUpperCase(),
            DateCreated: new Date().getTime(),
            Status: StatusOrder.new,
            Value: group.totalPrice,
            AddressId: address?.Id,
            Description: 'Giao hàng nhanh',
          };
          listOrder.push(order);
          for (const item of group.items) {
            const orderDetail = {
              Id: randomGID(),
              Name: item.Name,
              OrderId: order.Id,
              ProductId: item.ProductId,
              DateCreated: new Date().getTime(),
              Quantity: item.Quantity ?? 1,
              Price: item.Price,
              Discount: item.Discount ?? 0,
              Status: StatusOrder.new,
              Total:
                item.Price * item.Quantity - (item.Price * (item.Discount ?? 0)) / 100,
            };
            successfullyOrderedItemIds.push(item.id);
            listOrderDetail.push(orderDetail);
          }
        }
        const res = await orderDA.createOrder(listOrder);
        const detailRes = await orderDA.createOrderDetail(listOrderDetail);
        if (detailRes?.code === 200 && res?.code === 200) {
          // Lưu danh sách ID của các sản phẩm đã đặt hàng thành công
         if (successfullyOrderedItemIds.length > 0) {
          cartActions.removeItemsById(successfullyOrderedItemIds);
          setOrderSuccess(true);

          showSnackbar({
            message: 'Đặt hàng thành công!',
            status: ComponentStatus.SUCCSESS,
          });

          // Chuyển đến màn hình chi tiết đơn hàng
          // Lấy ID đơn hàng đầu tiên để xem chi tiết
          if (storeGroups.length > 0) {
            // Chuyển đến màn hình chi tiết đơn hàng
            navigation.replace(RootScreen.OrderDetailPage, {
              orderId: listOrder[0].Id,
            });
          } else {
            // Quay lại màn hình trước đó nếu không có đơn hàng
            navigation.goBack();
          }
        }
        }
      }
    } catch (error) {
      console.error('Error placing order:', error);
      showSnackbar({
        message: 'Đặt hàng thất bại. Vui lòng thử lại sau.',
        status: ComponentStatus.ERROR,
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Render một sản phẩm
  const renderProductItem = (item: CartItem) => {
    return (
      <View style={styles.productItemContainer} key={item.id}>
        <FastImage
          style={styles.productImage}
          source={{
            uri: item.Img?.startsWith('http')
              ? item.Img
              : `${ConfigAPI.urlImg}${item.Img}`,
            priority: FastImage.priority.normal,
          }}
          resizeMode={FastImage.resizeMode.cover}
        />

        <View style={styles.productDetails}>
          <View>
            <Text style={styles.productName}>{item.Name}</Text>
          </View>
          <Text style={styles.productPrice}>
            {Ultis.money(item.Price ?? 0)} đ
          </Text>
        </View>
      </View>
    );
  };

  // Render một nhóm cửa hàng
  const renderStoreGroup = (storeGroup: StoreGroup, index: number) => {
    return (
      <View style={styles.storeContainer} key={storeGroup.ShopId + index}>
        <View style={styles.storeHeader}>
          <FastImage
            style={styles.storeAvatar}
            source={{
              uri: storeGroup.ShopAvatar?.startsWith('http')
                ? storeGroup.ShopAvatar
                : `${ConfigAPI.urlImg}${storeGroup.ShopAvatar}`,
              priority: FastImage.priority.normal,
            }}
            resizeMode={FastImage.resizeMode.cover}
          />

          <Text style={styles.storeName}>{storeGroup.ShopName}</Text>
        </View>

        {storeGroup.items.map(item => renderProductItem(item))}

        <View style={styles.shippingContainer}>
          <View style={styles.shippingRow}>
            <AppSvg SvgSrc={iconSvg.delivery} size={20} />
            <Text style={styles.freeShippingText}>Free</Text>
          </View>

          <View style={styles.paymentRow}>
            <AppSvg SvgSrc={iconSvg.money} size={20} />
            <Text style={styles.paymentLabel}>Phương thức thanh toán:</Text>
            <Text style={styles.paymentMethod}>Ship COD</Text>
          </View>

          <View style={styles.storeTotalRow}>
            <AppSvg SvgSrc={iconSvg.moneyGold} size={20} />
            <Text style={styles.storeTotalLabel}>
              Tổng tiền ({storeGroup.items.length} sản phẩm):
            </Text>
            <Text style={styles.storeTotalPrice}>
              {Ultis.money(storeGroup.totalPrice ?? 0)} đ
            </Text>
          </View>
        </View>
      </View>
    );
  };

  // Render bottom bar với nút đặt hàng
  const renderBottomBar = () => {
    return (
      <View style={styles.bottomBar}>
        <View style={styles.totalContainer}>
          <Text style={styles.totalLabel}>Total:</Text>
          <Text style={styles.totalPrice}>${Ultis.money(totalPrice ?? 0)}</Text>
        </View>

        <TouchableOpacity
          style={[
            styles.placeOrderButton,
            isProcessing ? styles.placeOrderButtonDisabled : {},
          ]}
          onPress={handlePlaceOrder}
          disabled={isProcessing}>
          <Text style={styles.placeOrderButtonText}>Đặt hàng</Text>
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <SafeAreaView edges={['bottom']} style={styles.container}>
      <RenderHeaderCart title="Thanh toán và đặt hàng" />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollViewContent}
        showsVerticalScrollIndicator={false}>
        <RenderRecipientInfo customer={customer} />

        {storeGroups.map((group, index) => renderStoreGroup(group, index))}

        <View style={styles.bottomSpacer} />
      </ScrollView>

      {renderBottomBar()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    width: '100%',
    height: 140,
    position: 'relative',
  },
  headerBackground: {
    backgroundColor: '#2962FF',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
  },
  waveContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 2,
    overflow: 'hidden',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: Platform.OS === 'ios' ? 20 : STATUSBAR_HEIGHT + 10,
    height: 56,
    position: 'relative',
    zIndex: 3,
    left: 0,
    right: 0,
  },
  backButton: {
    position: 'absolute',
    left: 16,
    padding: 8,
  },
  headerTitle: {
    ...TypoSkin.heading5,
    fontSize: 20,
    fontWeight: '600',
    color: '#FFFFFF',
    textAlign: 'center',
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    paddingHorizontal: 16,
  },
  recipientInfoContainer: {
    backgroundColor: '#E6F7FF',
    borderRadius: 10,
    padding: 16,
    marginTop: 12,
    marginBottom: 8,
    elevation: 2,
    shadowColor: '#ccc',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  recipientHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  recipientTitle: {
    ...TypoSkin.heading6,
    color: '#000000',
  },
  editButton: {
    padding: 4,
  },
  recipientDetails: {
    gap: 4,
  },
  recipientName: {
    ...TypoSkin.body2,
    color: '#000000',
  },
  recipientAddress: {
    ...TypoSkin.body3,
    color: '#666666',
  },
  storeContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
    marginVertical: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  storeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  storeAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    marginRight: 8,
  },
  storeName: {
    ...TypoSkin.heading7,
    color: '#000000',
  },
  productItemContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  productImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginRight: 12,
  },
  productDetails: {
    flex: 1,
    flexDirection: 'column',
    // justifyContent: 'space-between',
    // alignItems: 'center',
  },
  productName: {
    ...TypoSkin.body2,
    color: '#000000',
    marginBottom: 4,
    fontSize: 14,
    fontWeight: '700',
  },
  productVariant: {
    ...TypoSkin.body3,
    color: '#666666',
  },
  productPrice: {
    ...TypoSkin.heading7,
    color: 'red',
    fontSize: 12,
    fontWeight: '700',
  },
  shippingContainer: {
    marginTop: 8,
  },
  shippingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  freeShippingText: {
    ...TypoSkin.body2,
    color: '#3FB993',
    marginLeft: 8,
    fontSize: 14,
    fontWeight: '700',
  },
  paymentRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  paymentLabel: {
    ...TypoSkin.body2,
    color: '#000000',
    marginLeft: 8,
    marginRight: 4,
    fontSize: 12,
    fontWeight: '700',
  },
  paymentMethod: {
    ...TypoSkin.body2,
    color: '#3FB993',
    lineHeight: 22,
    fontSize: 14,
    fontWeight: '700',
  },
  storeTotalRow: {
    flexDirection: 'row',
    // justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#EEEEEE',
  },
  storeTotalLabel: {
    ...TypoSkin.body2,
    color: '#000000',
    fontSize: 12,
    fontWeight: '700',
  },
  storeTotalPrice: {
    ...TypoSkin.heading7,
    fontSize: 14,
    fontWeight: '700',
    color: '#FF3B30',
    position: 'absolute',
    right: 2,
  },
  bottomBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: '#EEEEEE',
  },
  totalContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  totalLabel: {
    ...TypoSkin.heading6,
    color: '#000000',
    marginRight: 8,
  },
  totalPrice: {
    ...TypoSkin.heading6,
    color: '#FF3B30',
  },
  placeOrderButton: {
    backgroundColor: '#FFC107',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 24,
  },
  placeOrderButtonDisabled: {
    backgroundColor: '#CCCCCC',
  },
  placeOrderButtonText: {
    ...TypoSkin.heading7,
    color: '#FFFFFF',
  },
  bottomSpacer: {
    height: 100,
  },
});

export default CheckoutPage;
