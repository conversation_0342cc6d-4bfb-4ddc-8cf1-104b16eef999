export interface MapItem {
    formatted_address?: string;
    geometry: Geometry;
    name?: string;
    place_id?: string;
    plus_code?: string[];
    types?: string[];
    photos?: string[];
}


export interface Geometry {
    location: Location;
}

export interface Location {
    lat: number;
    lng: number;
}


export const mapItemsExample: MapItem[] = [
    // {
    //     formatted_address: '1600 Amphitheatre Parkway, Mountain View, CA',
    //     geometry: {
    //         location: {
    //             lat: 37.4221,
    //             lng: -122.0841,
    //         },
    //     },
    //     name: 'Googleplex',
    //     place_id: 'ChIJ2eUgeAK6j4ARbn5u_wAGqWA',
    //     photos: ['https://example.com/photo1.jpg', 'https://example.com/photo2.jpg'],
    // },
    // {
    //     formatted_address: '1 Infinite Loop, Cupertino, CA',
    //     geometry: {
    //         location: {
    //             lat: 37.3318,
    //             lng: -122.0312,
    //         },
    //     },
    //     name: 'Apple Park',
    //     place_id: 'ChIJ2eUgeAK6j4ARbn5u_wAGqWB',
    //     photos: ['https://example.com/photo3.jpg', 'https://example.com/photo4.jpg'],
    // },
    // Add more items as needed
];