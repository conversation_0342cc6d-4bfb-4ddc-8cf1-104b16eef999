import { Pressable } from "react-native";
import SkeletonPlaceholder from "react-native-skeleton-placeholder";

export const CardMapSearchSkeleton = () => {
    return (
        <Pressable style={{ paddingHorizontal: 12 }}>
            <SkeletonPlaceholder>
                <SkeletonPlaceholder.Item>
                    <SkeletonPlaceholder.Item marginTop={12}>
                        <SkeletonPlaceholder.Item height={22} />
                    </SkeletonPlaceholder.Item>
                    <SkeletonPlaceholder.Item marginTop={8}>
                        <SkeletonPlaceholder.Item height={16} width={200} />
                        <SkeletonPlaceholder.Item height={16} width={300} marginTop={8} />
                    </SkeletonPlaceholder.Item>
                </SkeletonPlaceholder.Item>
            </SkeletonPlaceholder>
        </Pressable>
    );
};