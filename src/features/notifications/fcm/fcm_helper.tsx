import notifee, {
  AndroidImportance,
  AuthorizationStatus,
  EventType,
} from '@notifee/react-native';
import messaging from '@react-native-firebase/messaging';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {PermissionsAndroid, Platform} from 'react-native';
import {navigate, navigationRef, RootScreen} from '../../../router/router';
import {getApp, initializeApp} from '@react-native-firebase/app';
import {getAuth} from '@react-native-firebase/auth';
import {NotificationActions} from '../../../redux/reducers/notificationReducer';
import store from '../../../redux/store/store';
import {CustomerActions} from '../../../redux/reducers/CustomerReducer';

// Define the CustomGlobal type
interface CustomGlobal {
  RNFB_SILENCE_MODULAR_DEPRECATION_WARNINGS: boolean;
  RNFB_MODULAR_DEPRECATION_STRICT_MODE: boolean;
}

declare var globalThis: CustomGlobal;

globalThis.RNFB_SILENCE_MODULAR_DEPRECATION_WARNINGS = true;
globalThis.RNFB_MODULAR_DEPRECATION_STRICT_MODE = true;

const requestNotificationPermission = async () => {
  if (Platform.OS === 'android') {
    try {
      PermissionsAndroid.check('android.permission.POST_NOTIFICATIONS')
        .then(response => {
          if (!response) {
            PermissionsAndroid.request(
              'android.permission.POST_NOTIFICATIONS',
              {
                title: 'Notification',
                message:
                  'App needs access to your notification ' +
                  'so you can get Updates',
                buttonNeutral: 'Ask Me Later',
                buttonNegative: 'Cancel',
                buttonPositive: 'OK',
              },
            );
          }
        })
        .catch(err => {
          console.log('Notification Error=====>', err);
        });
    } catch (err) {
      console.log(err);
    }
  }
};

// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const firebaseConfig = {
  apiKey: 'AIzaSyB2FfjSkUcJFpmNpNNm0tmexic_uJ-gxXs',
  authDomain: 'rncore-noti.firebaseapp.com',
  projectId: 'rncore-noti',
  storageBucket: 'rncore-noti.firebasestorage.app',
  messagingSenderId: '632449728851',
  appId: '1:632449728851:web:f587e392da47d2f73fa1a6',
  measurementId: 'G-KBFQHR375X',
};

let initFirebase;
try {
  initFirebase = getApp(); // This will get the default app if it's already initialized
  getAuth(initFirebase);
} catch (error) {
  initFirebase = initializeApp(firebaseConfig); // Initialize app with the provided config
}

export const initNotificationPermission = async () => {
  await registerAppWithFCM();

  const authStatus = await messaging().requestPermission();

  // ANDROID
  if (Platform.OS === 'android') {
    try {
      await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
      ).then(response => {
        if (response) {
          return getFcmToken();
        }
      });
      // requestNotificationPermission();
    } catch (err) {
      console.warn('requestNotificationPermission error: ', err);
    }
  } else {
    const enabled =
      authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
      authStatus === messaging.AuthorizationStatus.PROVISIONAL;
    // Request permissions (required for iOS)
    const settings = await notifee.requestPermission();
    if (settings.authorizationStatus === AuthorizationStatus.DENIED) {
      console.log('User denied permissions request');
    } else if (
      settings.authorizationStatus === AuthorizationStatus.AUTHORIZED
    ) {
      // console.log('User granted permissions request');
    } else if (
      settings.authorizationStatus === AuthorizationStatus.PROVISIONAL
    ) {
      console.log('User provisionally granted permissions request');
    }
    if (enabled) {
      return getFcmToken();
    }
  }
};

//method was called to listener events from firebase for notification triger
// main hàm xử lý thông tin và logic
export function registerListenerWithFCM() {
  const unsubscribe = messaging().onMessage(async remoteMessage => {
    // console.log('onMessage Received : ', JSON.stringify(remoteMessage));
    if (
      remoteMessage?.notification?.title &&
      remoteMessage?.notification?.body
    ) {
      // Increment badge count
      notifee
        .incrementBadgeCount()
        .then(() => notifee.getBadgeCount())
        .then(count => {
          console.log('Badge count incremented to: ', count);
        });
      NotificationActions.setBadge(store.dispatch);
      // show
      onDisplayNotification(
        remoteMessage.notification?.title,
        remoteMessage.notification?.body,
        remoteMessage?.data,
      );
    }
  });

  // xu ly noti click 2 luong
  notifee.onForegroundEvent(({type, detail}) => {
    // click khi hiển thị app hoac treo app
    switch (type) {
      case EventType.DISMISSED:
        notifee
          .decrementBadgeCount()
          .then(() => notifee.getBadgeCount())
          .then(count =>
            console.log('Badge count decremented by 1 to: ', count),
          );
        console.log('User dismissed notification', detail.notification);
        break;
      case EventType.PRESS:
        notifee
          .decrementBadgeCount()
          .then(() => notifee.getBadgeCount())
          .then(count =>
            console.log('Badge count decremented by 1 to: ', count),
          );
        console.log(
          'User pressed notification khi hiển thị app',
          detail.notification,
        );
        store.dispatch(CustomerActions.getInfor());
        navigate(RootScreen.NotifCommunity);
        break;
    }
    NotificationActions.setBadge(store.dispatch);
  });

  notifee.onBackgroundEvent(async ({type, detail}) => {
    // click khi k sd app
    const {notification, pressAction} = detail;
    if (type === EventType.PRESS) {
      notifee
        .decrementBadgeCount()
        .then(() => notifee.getBadgeCount())
        .then(count => console.log('Badge count decremented by 1 to: ', count));
      NotificationActions.setBadge(store.dispatch);
      store.dispatch(CustomerActions.getInfor());
      navigate(RootScreen.NotifCommunity);
      return;
    }
  });

  messaging().onNotificationOpenedApp(async remoteMessage => {
    console.log(
      'onNotificationOpenedApp Received',
      JSON.stringify(remoteMessage),
    );
  });

  // Check whether an initial notification is available
  messaging()
    .getInitialNotification()
    .then(remoteMessage => {
      if (remoteMessage) {
        console.log(
          'Notification caused app to open from quit state:',
          remoteMessage.notification,
        );
      }
    });

  return unsubscribe;
}

//method was called to get FCM tiken for notification
export const getFcmToken = async () => {
  let token = '';
  try {
    token = await messaging().getToken();
    console.log('getFcmToken-->', Platform.OS, token);
    await AsyncStorage.setItem('fcmToken', token);
  } catch (error) {
    console.log('getFcmToken Device Token error ', error);
  }
  return token;
};

//method was called on  user register with firebase FCM for notification
export async function registerAppWithFCM() {
  if (!messaging().isDeviceRegisteredForRemoteMessages) {
    await messaging()
      .registerDeviceForRemoteMessages()
      .then(status => {
        console.log('registerDeviceForRemoteMessages status', status);
      })
      .catch(error => {
        console.log('registerDeviceForRemoteMessages error ', error);
      });
  }
  messaging().subscribeToTopic('all-devices');
}

//method was called on un register the user from firebase for stoping receiving notifications
export async function unRegisterAppWithFCM() {
  console.log(
    'unRegisterAppWithFCM status',
    messaging().isDeviceRegisteredForRemoteMessages,
  );

  if (messaging().isDeviceRegisteredForRemoteMessages) {
    await messaging()
      .unregisterDeviceForRemoteMessages()
      .then(status => {
        console.log('unregisterDeviceForRemoteMessages status', status);
      })
      .catch(error => {
        console.log('unregisterDeviceForRemoteMessages error ', error);
      });
  }
  await messaging().deleteToken();
  console.log(
    'unRegisterAppWithFCM status',
    messaging().isDeviceRegisteredForRemoteMessages,
  );
}

export const decrementBadgeCount = async () => {
  notifee
    .decrementBadgeCount()
    .then(() => notifee.getBadgeCount())
    .then(count => console.log('Badge count decremented by 1 to: ', count));
  NotificationActions.setBadge(store.dispatch);
};

//method was called to display notification
async function onDisplayNotification(
  title: string,
  body: string,
  data:
    | {
        [key: string]: string | number | object;
      }
    | any
    | undefined,
) {
  // Create a channel (required for Android)
  const channelId = await notifee.createChannel({
    id: 'default',
    name: 'Default Channel',
  });

  // Display a notification
  await notifee.displayNotification({
    title: title,
    body: body,
    data: data,
    android: {
      channelId,
      importance: AndroidImportance.HIGH,
      // pressAction is needed if you want the notification to open the app when pressed
      pressAction: {
        id: 'default',
      },
    },
  });
}
