import React, { useEffect, useState, useRef, useCallback } from 'react';
import {
  View,
  StyleSheet,
  InteractionManager,
  ActivityIndicator,
  TouchableOpacity,
  Text,
  Animated,
  Image,
} from 'react-native';
import YoutubeIframe, { PLAYER_STATES } from 'react-native-youtube-iframe';
import { Winicon } from 'wini-mobile-components';

interface YouTubePlayerProps {
  videoId: string;
  height?: number;
  width?: number | string;
  play?: boolean;
  onReady?: () => void;
  onError?: (error: any) => void;
  onChangeState?: (state: string) => void;
  onPlaybackQualityChange?: (quality: string) => void;
  onFullScreenChange?: (isFullScreen: boolean) => void;
  playerParams?: {
    preventFullScreen?: boolean;
    cc_lang_pref?: string;
    showClosedCaptions?: boolean;
    controls?: boolean;
    modestbranding?: boolean;
  };
  useNativeControls?: boolean; // Thêm tùy chọn sử dụng nút điều khiển tùy chỉnh
}

/**
 * Component để nhúng và phát video YouTube trong ứng dụng React Native
 *
 * @param videoId - ID của video YouTube (bắt buộc)
 * @param height - Chiều cao của player (mặc định: 220)
 * @param width - Chiều rộng của player (mặc định: "100%")
 * @param play - Tự động phát video (mặc định: false)
 * @param onReady - Callback khi player đã sẵn sàng
 * @param onError - Callback khi có lỗi
 * @param onChangeState - Callback khi trạng thái player thay đổi
 * @param onPlaybackQualityChange - Callback khi chất lượng phát thay đổi
 * @param onFullScreenChange - Callback khi chế độ toàn màn hình thay đổi
 * @param playerParams - Các tham số cấu hình player
 * @param useNativeControls - Sử dụng nút điều khiển tùy chỉnh thay vì nút mặc định của YouTube
 */
const YouTubePlayer = ({
  videoId,
  height = 220,
  width = '100%',
  play: initialPlay = false,
  onReady,
  onError,
  onChangeState,
  onPlaybackQualityChange,
  onFullScreenChange,
  playerParams,
  useNativeControls = true, // Mặc định sử dụng nút điều khiển tùy chỉnh
}: YouTubePlayerProps) => {
  const [isReady, setIsReady] = useState(false);
  const [loading, setLoading] = useState(true);
  const [playing, setPlaying] = useState(initialPlay);
  const [_playerState, setPlayerState] = useState('');
  // Luôn hiển thị nút play khi mới init video
  const [showControls, setShowControls] = useState(true);
  // Theo dõi trạng thái đã nhấn play chưa
  const [hasPlayedOnce, setHasPlayedOnce] = useState(false);
  // Theo dõi thời gian hiện tại và tổng thời lượng của video
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  // Theo dõi trạng thái full screen
  const [isFullScreen, setIsFullScreen] = useState(false);

  const playerRef = useRef(null);
  const controlsTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const fadeAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    // Đợi cho đến khi các tương tác chính (animation, transitions) hoàn thành
    // trước khi render video nặng
    InteractionManager.runAfterInteractions(() => {
      setIsReady(true);
    });
  }, []);

  // Cập nhật trạng thái playing khi prop play thay đổi
  useEffect(() => {
    setPlaying(initialPlay);
  }, [initialPlay]);

  // Ẩn controls
  const hideControls = useCallback(() => {
    setShowControls(false);
  }, []);

  // Xử lý hiển thị/ẩn controls
  useEffect(() => {
    if (showControls) {
      // Hiển thị controls với animation
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }).start();

      // Chỉ tự động ẩn controls sau khi đã nhấn play ít nhất một lần
      // và video đang phát
      if (hasPlayedOnce && playing) {
        if (controlsTimeoutRef.current) {
          clearTimeout(controlsTimeoutRef.current);
        }

        controlsTimeoutRef.current = setTimeout(() => {
          hideControls();
        }, 3000);
      }
    } else {
      // Ẩn controls với animation
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }).start();
    }

    // Cleanup timeout khi component unmount
    return () => {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
      }
    };
  }, [showControls, fadeAnim, hideControls, hasPlayedOnce, playing]);

  // Hàm để cập nhật thời gian hiện tại của video
  const onProgress = useCallback((data: { currentTime: number }) => {
    setCurrentTime(data.currentTime);
  }, []);

  // Hàm để cập nhật tổng thời lượng của video
  const onDuration = useCallback((duration: number) => {
    setDuration(duration);
  }, []);

  // Sử dụng interval để cập nhật thời gian hiện tại khi video đang phát
  useEffect(() => {
    let intervalId: NodeJS.Timeout | null = null;

    if (playing && playerRef.current) {
      // Cập nhật thời gian hiện tại mỗi 1 giây
      intervalId = setInterval(async () => {
        try {
          // Sử dụng các phương thức của playerRef để lấy thời gian hiện tại và tổng thời lượng
          if (playerRef.current) {
            // @ts-ignore - Bỏ qua lỗi TypeScript vì chúng ta biết phương thức này tồn tại
            const currentVideoTime = await playerRef.current.getCurrentTime();
            if (currentVideoTime) {
              setCurrentTime(currentVideoTime);
            }

            // Chỉ cập nhật tổng thời lượng nếu chưa có
            if (duration === 0 && playerRef.current) {
              // @ts-ignore
              const videoDuration = await playerRef.current.getDuration();
              if (videoDuration) {
                setDuration(videoDuration);
              }
            }
          }
        } catch (error) {
          console.log('Error updating time:', error);
        }
      }, 1000);
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [playing, duration]);

  // Hàm để xử lý khi người dùng nhấn nút full screen
  const handleFullScreenToggle = useCallback(() => {
    setIsFullScreen(prev => !prev);
    if (onFullScreenChange) {
      onFullScreenChange(!isFullScreen);
    }
  }, [isFullScreen, onFullScreenChange]);

  // Hàm để định dạng thời gian (chuyển từ giây sang định dạng mm:ss)
  const formatTime = useCallback((timeInSeconds: number) => {
    if (isNaN(timeInSeconds) || timeInSeconds < 0) {
      return '0:00';
    }

    const minutes = Math.floor(timeInSeconds / 60);
    const seconds = Math.floor(timeInSeconds % 60);
    return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
  }, []);

  // Hàm để tính toán phần trăm tiến trình
  const getProgressPercent = useCallback(() => {
    if (duration <= 0 || isNaN(duration)) {
      return 0;
    }

    return Math.min((currentTime / duration) * 100, 100);
  }, [currentTime, duration]);

  const handleReady = () => {
    setLoading(false);

    // Khi video đã sẵn sàng, luôn hiển thị nút play tùy chỉnh
    setShowControls(true);

    // Chỉ bắt đầu đếm thời gian để ẩn nút play nếu video đã được play ít nhất một lần
    // và hiện đang phát
    if (hasPlayedOnce && playing) {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
      }

      controlsTimeoutRef.current = setTimeout(() => {
        hideControls();
      }, 3000);
    }

    if (onReady) {
      onReady();
    }
  };

  const handleStateChange = useCallback((state: string) => {
    setPlayerState(state);

    if (state === PLAYER_STATES.ENDED) {
      setPlaying(false);
      setShowControls(true); // Hiển thị controls khi video kết thúc
    }

    if (onChangeState) {
      onChangeState(state);
    }
  }, [onChangeState]);

  const togglePlaying = useCallback(() => {
    // Cập nhật trạng thái playing
    setPlaying(prev => {
      const newPlayingState = !prev;

      // Nếu chuyển sang trạng thái play, đánh dấu đã play ít nhất một lần
      if (newPlayingState) {
        setHasPlayedOnce(true);
      }

      return newPlayingState;
    });
  }, []);

  // Hiển thị controls
  const showControlsHandler = useCallback(() => {
    setShowControls(true);
  }, []);

  // Xử lý khi người dùng nhấn vào video
  const handleVideoPress = useCallback(() => {
    // Nếu chưa từng play hoặc video đang dừng, luôn hiển thị nút play và toggle play/pause
    if (!hasPlayedOnce || !playing) {
      togglePlaying();
    }
    // Nếu đã play ít nhất một lần và video đang phát
    else {
      if (showControls) {
        // Nếu controls đang hiển thị, toggle play/pause
        togglePlaying();
      } else {
        // Nếu controls đang ẩn, hiển thị controls ngay lập tức
        showControlsHandler();
      }
    }
  }, [showControls, togglePlaying, showControlsHandler, hasPlayedOnce, playing]);

  if (!isReady) {
    return <View style={[styles.placeholder, { height }]} />;
  }

  return (
    <View style={[styles.container, { height }]}>
      {loading && (
        <View style={[styles.loadingContainer, { height }]}>
          <ActivityIndicator size="large" color="#0000ff" />
        </View>
      )}

      <YoutubeIframe
        ref={playerRef}
        height={height}
        width={width}
        videoId={videoId}
        play={playing}
        onReady={handleReady}
        onError={onError}
        onChangeState={handleStateChange}
        onPlaybackQualityChange={onPlaybackQualityChange}
        onFullScreenChange={onFullScreenChange}
        // Thêm sự kiện để cập nhật thời gian hiện tại
        onCurrentTime={onProgress}
        // Thêm sự kiện để cập nhật tổng thời lượng
        onDuration={onDuration}
        webViewProps={{
          androidLayerType: 'hardware',
          renderToHardwareTextureAndroid: true,
          androidHardwareAccelerationDisabled: false,
          javaScriptEnabled: true,
          domStorageEnabled: true,
          allowsInlineMediaPlayback: true,
          mediaPlaybackRequiresUserAction: false,
        }}
        initialPlayerParams={{
          preventFullScreen: playerParams?.preventFullScreen,
          cc_lang_pref: playerParams?.cc_lang_pref,
          showClosedCaptions: playerParams?.showClosedCaptions,
          // Luôn tắt controls mặc định của YouTube
          controls: 0, // Tắt hoàn toàn controls mặc định
          modestbranding: 1,
          rel: 0, // Không hiển thị video liên quan
          fs: 0, // Tắt nút full screen mặc định
          iv_load_policy: 3, // Ẩn chú thích video
          showinfo: 0, // Ẩn tiêu đề video và người tải lên
          autohide: 1, // Tự động ẩn các điều khiển sau khi phát
        }}
      />

      {/* Overlay để bắt sự kiện nhấn - hiển thị ngay cả khi đang tải */}
      {useNativeControls && (
        <TouchableOpacity
          activeOpacity={1}
          style={styles.controlOverlay}
          onPress={handleVideoPress}
        >
          {/* Nút play/pause ở giữa - chỉ hiển thị khi showControls = true */}
          <Animated.View
            style={[
              styles.controlsContainer,
              { opacity: fadeAnim },
            ]}
          >
            {/* Nút play/pause ở giữa */}
            <View style={styles.playPauseButton}>
              {
                !playing ? (
                  <Winicon
                    src="color/social media/logo-youtube"
                    size={70}
                  />
                ) : (
                  <Winicon
                    src="fill/multimedia/btn-play"
                    size={30}
                    color="#fff"
                  />
                )
              }
            </View>

            {/* Thanh điều khiển phía dưới */}
            {hasPlayedOnce && (
              <View style={styles.bottomControls}>
                {/* Thanh tiến trình */}
                <View style={styles.progressContainer}>
                  <View style={styles.progressBackground}>
                    <View
                      style={[
                        styles.progressBar,
                        { width: `${getProgressPercent()}%` },
                      ]}
                    />
                  </View>
                  <View style={styles.timeContainer}>
                    <Text style={styles.timeText}>{formatTime(currentTime)}</Text>
                    <Text style={styles.timeText}>{formatTime(duration)}</Text>
                  </View>
                </View>

                {/* Nút full screen */}
                <TouchableOpacity
                  style={styles.fullscreenButton}
                  onPress={handleFullScreenToggle}
                >
                  <Winicon
                    src={isFullScreen ? 'outline/arrows/minimize' : 'outline/arrows/maximize'}
                    size={24}
                    color="#fff"
                  />
                </TouchableOpacity>
              </View>
            )}
          </Animated.View>
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    // padding: 10,
    position: 'relative',
    backgroundColor: '#f0f0f0',
    borderRadius: 10,
    width: '100%',
    // aspectRatio: 16 / 9,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  placeholder: {
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(240, 240, 240, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  controlOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'transparent',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 2,
  },
  // Container cho tất cả các điều khiển
  controlsContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'column',
  },
  // Nút play/pause ở giữa
  playPauseButton: {
    borderRadius: 35,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    top: '30%',
    width: 70,
    height: 70,
    // Thêm shadow để nút nổi bật hơn
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.8,
    shadowRadius: 3,
    elevation: 5,
  },
  playPauseIcon: {
    fontSize: 30,
    color: 'white',
  },
  // Thanh điều khiển phía dưới
  bottomControls: {
    position: 'absolute',
    bottom: 10,
    left: 10,
    right: 10,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 5,
    padding: 5,
  },
  // Container cho thanh tiến trình
  progressContainer: {
    flex: 1,
    marginRight: 10,
  },
  // Nền của thanh tiến trình
  progressBackground: {
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 2,
    overflow: 'hidden',
  },
  // Thanh tiến trình
  progressBar: {
    height: '100%',
    backgroundColor: '#ff0000',
    borderRadius: 2,
  },
  // Container cho thời gian
  timeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 5,
  },
  // Text hiển thị thời gian
  timeText: {
    color: 'white',
    fontSize: 12,
  },
  // Nút full screen
  fullscreenButton: {
    width: 25,
    height: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default YouTubePlayer;