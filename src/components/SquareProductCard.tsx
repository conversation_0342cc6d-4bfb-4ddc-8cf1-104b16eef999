import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ImageSourcePropType,
  Dimensions,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import {Winicon} from 'wini-mobile-components';
import ConfigAPI from '../Config/ConfigAPI';
import {Ultis} from '../utils/Utils';
import {TypoSkin} from '../assets/skin/typography';
import {ColorThemes} from '../assets/skin/colors';

const {width} = Dimensions.get('window');

// <PERSON><PERSON>ch thước mặc định của card sản phẩm
const DEFAULT_ITEM_WIDTH = width * 0.42;
const DEFAULT_ITEM_HEIGHT = DEFAULT_ITEM_WIDTH * 1.5;

export interface SquareProductItem {
  Id: string;
  Name: string;
  Price: number;
  Img: string;
  rating?: number;
  soldCount?: number;
  Description?: string;
}

interface SquareProductCardProps {
  item: SquareProductItem;
  onPress?: (item: SquareProductItem) => void;
  width?: number;
  height?: number;
  showRating?: boolean;
}

const SquareProductCard: React.FC<SquareProductCardProps> = ({
  item,
  onPress,
  width: cardWidth = DEFAULT_ITEM_WIDTH,
  height: cardHeight = DEFAULT_ITEM_HEIGHT,
  showRating = true,
}) => {
  return (
    <TouchableOpacity
      style={[styles.itemContainer, {width: 141, height: 241}]}
      onPress={() => onPress && onPress(item)}
      activeOpacity={0.8}>
      {/* Ảnh sản phẩm */}
      <View style={styles.imageContainer}>
        <FastImage
          source={{uri: ConfigAPI.urlImg + item.Img}}
          style={styles.image}
          resizeMode={FastImage.resizeMode.cover}
        />
      </View>

      {/* Thông tin sản phẩm */}
      <View style={styles.infoContainer}>
        <Text style={styles.title} numberOfLines={2}>
          {item.Name}
        </Text>

        {item.Description && (
          <Text style={styles.description} numberOfLines={1}>
            {item.Description}
          </Text>
        )}

        <View style={styles.bottomContainer}>
          {showRating && (
            <View style={styles.ratingContainer}>
              <Winicon
                src="fill/user interface/star"
                size={12}
                color={ColorThemes.light.warning_main_color}
              />
              <Text style={styles.ratingText}>{item.rating || 4.5}</Text>
              <Text style={styles.soldText}>
                | Đã bán {item.soldCount || 200}
              </Text>
            </View>
          )}
          <Text style={styles.price}>{Ultis.money(item.Price)} đ</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  itemContainer: {
    borderRadius: 17,
    backgroundColor: '#fff',
    overflow: 'hidden',
    // marginRight: 10,
  },
  imageContainer: {
    width: '100%',
    height: '60%',
    position: 'relative',
  },
  image: {
    width: '100%',
    height: '100%',
    borderRadius: 17,
  },
  infoContainer: {
    paddingVertical: 8,
    flex: 1,
    justifyContent: 'space-between',
  },
  title: {
    ...TypoSkin.title4,
    marginBottom: 4,
  },
  description: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  bottomContainer: {
    marginTop: 'auto',
    gap: 10,
  },
  price: {
    fontSize: 14,
    fontWeight: '700',
    color: '#FF3B30',
    marginBottom: 2,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    ...TypoSkin.subtitle4,
    color: ColorThemes.light.warning_main_color,
    marginLeft: 4,
  },
  soldText: {
    ...TypoSkin.subtitle4,
    color: ColorThemes.light.neutral_text_subtitle_color,
    marginLeft: 4,
  },
});

export default SquareProductCard;
