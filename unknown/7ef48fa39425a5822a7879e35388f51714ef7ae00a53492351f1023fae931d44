import {useNavigation} from '@react-navigation/native';
import {
  KeyboardAvoidingView,
  Pressable,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {useForm} from 'react-hook-form';
import {ColorThemes} from '../../../assets/skin/colors';
import {DropdownForm, TextFieldForm} from '../../Default/form/component-form';
import {AppButton, Winicon} from 'wini-mobile-components';
import {validatePhoneNumber} from '../../../utils/validate';
import DatePicker from 'react-native-date-picker';
import {format} from 'date-fns';
import {TypoSkin} from '../../../assets/skin/typography';
import {Ultis} from '../../../utils/Utils';
import {useTranslation} from 'react-i18next';

export default function EditCustomer({
  methods,
  bankList,
}: {
  methods: any;
  bankList: any;
}) {
  const {t} = useTranslation();
  const [open, setOpen] = useState(false);
  const [date, setDate] = useState<any>(new Date());

  useEffect(() => {
    if (methods.getValues('Dob')) {
      setDate(new Date(methods.getValues('Dob')));
    }
  }, []);

  return (
    <Pressable
      style={{
        flex: 1,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <KeyboardAvoidingView>
        <View
          style={{
            paddingHorizontal: 16,
            gap: 16,
            paddingVertical: 8,
            paddingBottom: 100,
          }}>
          <TextFieldForm
            required
            style={{width: '100%', backgroundColor: '#fff', borderRadius: 8}}
            placeholder={t('profile.name')}
            label={t('profile.name')}
            control={methods.control}
            errors={methods.formState.errors}
            register={methods.register}
            name="Name"
            textFieldStyle={{padding: 16}}
          />
          <TextFieldForm
            required
            style={{width: '100%', backgroundColor: '#fff', borderRadius: 8}}
            placeholder={t('profile.email')}
            label={t('profile.email')}
            disabled
            control={methods.control}
            errors={methods.formState.errors}
            register={methods.register}
            name="Email"
            textFieldStyle={{padding: 16}}
          />
          <TextFieldForm
            style={{width: '100%', backgroundColor: '#fff', borderRadius: 8}}
            placeholder={t('profile.phone')}
            label={t('profile.phone')}
            control={methods.control}
            register={methods.register}
            errors={methods.formState.errors}
            name="Mobile"
            textFieldStyle={{padding: 16}}
            type="number-pad"
            onBlur={(ev: string) => {
              // Check if the number doesn't already start with 0 or +84
              if (/^(\+84|0)/.test(ev)) {
                const val = validatePhoneNumber(ev);
                if (!val) {
                  methods.setError('Mobile', {
                    message: t('profile.invalidPhone'),
                  });
                  return;
                }
              }
              if (ev?.length !== 0) methods.clearErrors('Mobile');
              else
                methods.setError('Mobile', {
                  message: t('profile.phoneRequired'),
                });
            }}
          />
          <TouchableOpacity
            style={{width: '100%', gap: 8}}
            onPress={() => setOpen(true)}>
            <Text
              style={{
                ...TypoSkin.label3,
                color: ColorThemes.light.neutral_text_title_color,
              }}>
              {t('profile.selectBirthdate')}
            </Text>
            <View
              style={{
                width: '100%',
                backgroundColor: '#fff',
                borderRadius: 8,
                paddingHorizontal: 16,
                paddingVertical: 12,
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                borderColor: ColorThemes.light.neutral_main_border_color,
                borderWidth: 1,
              }}>
              <Text>
                {Ultis.datetoString(new Date(methods.watch('Dob') ?? 0))}
              </Text>
            </View>
          </TouchableOpacity>
          <DatePicker
            modal
            open={open}
            date={date}
            mode="date"
            minimumDate={new Date('1900-01-01')}
            maximumDate={new Date()}
            onConfirm={selectedDate => {
              setOpen(false);
              setDate(selectedDate);
              methods.setValue('Dob', selectedDate.getTime());
            }}
            onCancel={() => {
              setOpen(false);
            }}
          />
          {/* <FAddressPickerForm
            control={methods.control}
            errors={methods.formState.errors}
            name="Address"
            label="Địa chỉ"
            placeholder="Nhập địa chỉ của bạn"
            placeName={''}
            onChange={value => {
              methods.setValue('Long', value.geometry.location.lng);
              methods.setValue('Lat', value.geometry.location.lat);
              methods.setValue('Address', value.formatted_address);
              return value.formatted_address;
            }}
          /> */}
          <DropdownForm
            placeholder={t('profile.gender')}
            label={t('profile.gender')}
            control={methods.control}
            errors={methods.formState.errors}
            style={{width: '100%', backgroundColor: '#fff', borderRadius: 8}}
            name="Gender"
            options={[
              {id: 1, name: t('profile.male')},
              {id: 2, name: t('profile.female')},
            ]}
          />
          <TextFieldForm
            control={methods.control}
            name="Address"
            errors={methods.formState.errors}
            label={t('profile.address')}
            style={{backgroundColor: ColorThemes.light.transparent}}
            textFieldStyle={{
              height: 100,
              paddingHorizontal: 16,
              paddingTop: 16,
              paddingBottom: 16,
              justifyContent: 'flex-start',
              backgroundColor: ColorThemes.light.transparent,
            }}
            textStyle={{textAlignVertical: 'top'}}
            numberOfLines={10}
            multiline={true}
            register={methods.register}
          />
        </View>
      </KeyboardAvoidingView>
    </Pressable>
  );
}
