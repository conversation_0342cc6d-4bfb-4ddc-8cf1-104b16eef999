import { Mo<PERSON>, SafeAreaView, StyleSheet, View } from "react-native";
import { MapItem, mapItemsExample } from "../da";
import React from "react";
import MapGeolocationView from "../views/map-geolocation-view";
import SearchMapAutocomplete from "../views/search-map";


interface DialogState {
    readonly open?: boolean,
    title: string,
    step?: number,
    suggestList?: Array<MapItem>,
    milestoneLocation?: { longitude: number, latitude: number }
    onSubmit: (location: MapItem) => void,
}


export const showDialogMap = ({ ref, title, suggestList, milestoneLocation, onSubmit }: {
    ref: React.MutableRefObject<DialogSearchMap>,
    title?: string,
    milestoneLocation?: { longitude: number, latitude: number },
    suggestList?: Array<MapItem>,
    onSubmit?: (location: MapItem) => void,
}) => {
    ref.current.showDialogNoti({
        title: title ?? '',
        onSubmit: onSubmit ?? ((location) => { }),
    })
}

export class DialogSearchMap extends React.Component<Object, DialogState> {
    state: Readonly<DialogState> = {
        open: false,
        title: '',
        step: 0,
        suggestList: mapItemsExample,
        onSubmit: (location) => { }
    }

    showDialogNoti(data: DialogState) {
        this.setState({ open: true, ...data })
    }

    closeDialog() {
        this.setState({ open: false })
    }

    render() {
        return (
            <Modal visible={
                this.state.open ?? false
            } transparent animationType="slide">
                {
                    this.state.step == 0 ? <View style={styles.overlay}>
                        <SafeAreaView />
                        <SearchMapAutocomplete
                            suggestList={this.state.suggestList}
                            title={this.state.title}
                            onChangeMapPicker={() => {
                                this.setState({ step: this.state.step == 0 ? 1 : 0 })
                            }}
                            onSelectLocation={(value) => {
                                console.log(value);
                                this.state.onSubmit(value);
                                this.closeDialog()
                            }} onClose={() => {
                                this.closeDialog()
                            }} />
                    </View> : <MapGeolocationView
                        onBack={() => { this.setState({ step: 0 }) }}
                        onDone={(value: any) => {
                            console.log('=================MapPicker===================');
                            console.log(value);
                            this.state.onSubmit(value);
                            this.setState({ step: 0 })
                            this.closeDialog()
                            // console.log('====================================');
                        }} />
                }
            </Modal>
        )
    }
}


const styles = StyleSheet.create({
    overlay: {
        alignItems: 'center',
        height: '100%',
        width: '100%',
        flex: 1,
        justifyContent: 'center',
        backgroundColor: '#00000080',
    },

});


