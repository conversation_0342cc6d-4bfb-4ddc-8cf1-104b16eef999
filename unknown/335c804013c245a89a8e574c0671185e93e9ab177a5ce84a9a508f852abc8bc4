import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { AppSvg, Winicon } from 'wini-mobile-components';
import { ColorThemes } from '../assets/skin/colors';
import { TypoSkin } from '../assets/skin/typography';
import { navigate, RootScreen } from '../router/router';
import iconSvg from '../svg/icon';
import { useCartState } from '../redux/hook/cartHook';

interface CartIconProps {
  color?: string;
  size?: number;
  isHome: boolean;
  showBadge?: boolean;
  onPress?: () => void;
}

const CartIcon: React.FC<CartIconProps> = ({
  color = ColorThemes.light.neutral_text_title_color,
  size = 24,
  showBadge = true,
  isHome = true,
  onPress,
}) => {
  // Lấy state của giỏ hàng
  const cartState = useCartState();
  
  // Tính tổng số lượng sản phẩm trong giỏ hàng
  const itemCount = cartState.items.length;
  
  // Xử lý khi nhấn vào biểu tượng giỏ hàng
  const handlePress = () => {
    if (onPress) {
      onPress();
    } else {
      navigate(RootScreen.CartPage);
    }
  };
  
  return (
    <TouchableOpacity style={styles.container} onPress={handlePress}>
      {isHome ? (
        <AppSvg SvgSrc={iconSvg.card} size={16} />
      ) : (
        <Winicon src="outline/shopping/cart" size={size} color={color} />
      )}
      {/* <AppSvg SvgSrc={iconSvg.card} size={16} /> */}

      {showBadge && itemCount > 0 && (
        <View style={styles.badge}>
          <Text style={styles.badgeText}>
            {itemCount > 99 ? '99+' : itemCount}
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    padding: 4,
  },
  badge: {
    position: 'absolute',
    top: -10,
    right: -10,
    backgroundColor: ColorThemes.light.error_main_color,
    borderRadius: 10,
    minWidth: 18,
    height: 18,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  badgeText: {
    ...TypoSkin.subtitle4,
    fontSize: 10,
    color: ColorThemes.light.neutral_absolute_background_color,
    fontWeight: '700',
  },
});

export default CartIcon;
