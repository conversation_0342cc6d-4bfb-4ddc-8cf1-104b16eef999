import {ScrollView, Text, TouchableOpacity, View} from 'react-native';
import TitleWithBottom from '../../Screen/Layout/titleWithBottom';
import DefaultList from '../Default/listview/default';
import {
  ComponentStatus,
  ListTile,
  showSnackbar,
  SkeletonImage,
  Winicon,
} from 'wini-mobile-components';
import {ColorThemes} from '../../assets/skin/colors';
import {TypoSkin} from '../../assets/skin/typography';
import {navigate, navigateReset, RootScreen} from '../../router/router';
import {CustomerActions} from '../../redux/reducers/CustomerReducer';
import {useDispatch} from 'react-redux';
import ConfigAPI from '../../Config/ConfigAPI';
import {BaseDA} from '../../base/BaseDA';
import ImagePicker from 'react-native-image-crop-picker';
import {CustomerRankType} from '../../Config/Contanst';
import {useEffect, useState} from 'react';
import {DrawerActions, useNavigation, useRoute} from '@react-navigation/native';
import { useSelectorCustomerState } from '../../redux/hook/customerHook';

const actionList = [
  {
    id: 0,
    name: 'Tài khoản',
    icon: 'fill/users/profile',
    background: ColorThemes.light.secondary5_background,
    colorIcon: ColorThemes.light.secondary5_main_color,
    route: RootScreen.SettingProfile,
  },

  {
    id: 3,
    name: 'Touch ID/Face ID',
    icon: 'fill/technology/face-recognition',
    background: ColorThemes.light.infor_background,
    colorIcon: ColorThemes.light.infor_main_color,
    route: RootScreen.BiometricSetting,
  },
  // {
  //   id: 4,
  //   name: 'Khoá học của tôi',
  //   icon: 'outline/shopping/list',
  //   action: 'mycourse',
  //   route: RootScreen.navigateESchoolView,
  // },
  {
    id: 5,
    name: 'Lịch sử mua hàng',
    icon: 'fill/shopping/shopping-cart',
    background: ColorThemes.light.secondary4_background,
    colorIcon: ColorThemes.light.secondary4_main_color,
    route: RootScreen.PurchaseHistory,
  },
  {
    id: 7,
    name: 'FAQ',
    show: true,
    icon: 'fill/layout/circle-question',
    background: ColorThemes.light.warning_background,
    colorIcon: ColorThemes.light.warning_main_color,
    route: RootScreen.FAQView,
  },
  {
    id: 1,
    name: 'Chính sách',
    show: true,
    icon: 'fill/shopping/list',
    background: ColorThemes.light.success_background,
    colorIcon: ColorThemes.light.success_main_color,
    route: RootScreen.PolicyView,
  },
  {
    id: 6,
    name: 'Đăng xuất',
    show: true,
    action: 'logout',
    icon: 'outline/arrows/logout',
    background: ColorThemes.light.error_background,
    colorIcon: ColorThemes.light.error_main_color,
    route: RootScreen.login,
  },
];

export default function Profile() {
  const dispatch = useDispatch<any>();
  const customer = useSelectorCustomerState().data;

  const [avt, setAvt] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const route = useRoute<any>();

  useEffect(() => {
    if (customer) {
      setAvt(customer.AvatarUrl);
    }
  }, [customer]);

  const pickerImg = async () => {
    const img = await ImagePicker.openPicker({
      multiple: false,
      cropping: true,
      cropperCircleOverlay: true,
    });
    if (img) {
      const resImgs = await BaseDA.uploadFiles([
        {
          uri: img.path,
          type: img.mime,
          name: img.filename ?? 'new file img',
        },
      ]);
      if (resImgs) {
        setIsLoading(true);
        await dispatch(
          CustomerActions.edit({
            ...customer,
            AvatarUrl: resImgs[0].Id,
          }),
        ).then(() => {
          setAvt(resImgs[0].Id);
          setIsLoading(false);
          showSnackbar({
            message: 'Cập nhật ảnh đại diện thành công',
            status: ComponentStatus.SUCCSESS,
            bottom: 60,
          });
          dispatch(CustomerActions.getInfor());
        });
      }
    }
  };
  const navigation = useNavigation<any>();

  return (
    <TitleWithBottom
      title="Cá nhân"
      prefix={
        <TouchableOpacity
          onPress={() => navigation.dispatch(DrawerActions.toggleDrawer())}
          style={{padding: 4}}>
          <Winicon src="fill/user interface/apps" size={20} />
        </TouchableOpacity>
      }
      iconActionPress={() => {
        navigate(RootScreen.Notification);
      }}>
      <ScrollView
        style={{
          flex: 1,
          backgroundColor: ColorThemes.light.neutral_main_background_color,
        }}>
        {customer ? (
          <View
            style={{
              margin: 16,
              padding: 24,
              gap: 16,
              borderRadius: 8,
              backgroundColor:
                ColorThemes.light.neutral_absolute_background_color,
            }}>
            <View
              style={{
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'space-between',
              }}>
              <TouchableOpacity
                style={{
                  width: 56,
                  height: 56,
                  borderRadius: 100,
                  backgroundColor:
                    ColorThemes.light.neutral_main_background_color,
                }}
                onPress={customer ? pickerImg : undefined}>
                {isLoading ? (
                  <SkeletonImage
                    style={{width: '100%', height: '100%', borderRadius: 100}}
                  />
                ) : (
                  <SkeletonImage
                    source={{uri: ConfigAPI.urlImg + avt}}
                    style={{width: '100%', height: '100%', borderRadius: 100}}
                  />
                )}
                <View
                  style={{
                    position: 'absolute',
                    padding: 5,
                    borderRadius: 24,
                    backgroundColor: '#fff',
                    right: -2,
                    bottom: -2,
                  }}>
                  <Winicon
                    src="fill/entertainment/camera"
                    size={10}
                    color={'#000'}
                  />
                </View>
              </TouchableOpacity>
            </View>
            <View style={{gap: 8}}>
              <View>
                <Text
                  style={{
                    ...TypoSkin.heading6,
                    color: ColorThemes.light.neutral_text_title_color,
                  }}>
                  {customer?.Name ?? customer?.Email ?? ''}
                </Text>
                {customer?.Rank ? (
                  <Text
                    style={{
                      ...TypoSkin.subtitle3,
                      color: ColorThemes.light.neutral_text_subtitle_color,
                    }}>
                    Hạng{' '}
                    {customer?.Rank == CustomerRankType.normal
                      ? 'Thường'
                      : 'VIP'}
                  </Text>
                ) : null}
              </View>
              {customer?.Description ? (
                <Text
                  style={{
                    ...TypoSkin.body3,
                    color: ColorThemes.light.neutral_text_body_color,
                  }}>
                  {customer?.Description ?? '-'}
                </Text>
              ) : null}
            </View>
          </View>
        ) : null}
        {(!customer ? actionList.filter(item => item.show) : actionList).map(
          item => (
            <ListTile
              key={item.id}
              style={{
                padding: 0,
              }}
              listtileStyle={{
                paddingRight: 16,
                paddingVertical: 13,
                gap: 8,
                borderBottomColor: ColorThemes.light.neutral_main_border_color,
                borderBottomWidth: 1,
                marginLeft: 16,
              }}
              onPress={() => {
                if (item.action === 'logout') {
                  // Handle logout action here
                  console.log('Logout action triggered');
                  // navigateReset(RootScreen.login);
                  dispatch(CustomerActions.logout());
                  return;
                }
                if (item.action === 'mycourse') {
                  navigation.navigate(RootScreen.navigateEComView, {
                    screen: 'Learn',
                  });
                  return;
                }
                navigate(item.route);
              }}
              leading={
                <View
                  style={{
                    height: 32,
                    width: 32,
                    borderRadius: 4,
                    padding: 6,
                    backgroundColor: item.background,
                  }}>
                  <Winicon src={item.icon} color={item.colorIcon} size={20} />
                </View>
              }
              title={
                !customer && item.action === 'logout' ? 'Đăng nhập' : item.name
              }
              titleStyle={[
                TypoSkin.heading8,
                {color: ColorThemes.light.neutral_text_title_color},
              ]}
              trailing={
                <Winicon
                  src="outline/arrows/right-arrow"
                  color={ColorThemes.light.neutral_text_subtitle_color}
                  size={16}
                />
              }
            />
          ),
        )}
      </ScrollView>
    </TitleWithBottom>
  );
}
