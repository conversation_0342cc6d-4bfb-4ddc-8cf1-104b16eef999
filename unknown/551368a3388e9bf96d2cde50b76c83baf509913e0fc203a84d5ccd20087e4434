import React, {useEffect, useMemo, useRef, useState} from 'react';
import {
  Dimensions,
  Image,
  KeyboardAvoidingView,
  Pressable,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {useDispatch} from 'react-redux';
import {
  showSnackbar,
  ComponentStatus,
  FLoading,
  Winicon,
  AppButton,
  ListTile,
  FDialog,
  showDialog,
} from 'wini-mobile-components';
import {ColorThemes} from '../../assets/skin/colors';
import {TypoSkin} from '../../assets/skin/typography';
import {DataController} from '../../base/baseController';
import LocalAuthen from '../../features/local-authen/local-authen';
import {navigateReset, RootScreen} from '../../router/router';
import {
  saveDataToAsyncStorage,
  getDataToAsyncStorage,
  removeDataToAsyncStorage,
} from '../../utils/AsyncStorage';
import {randomGID, regexPassWord} from '../../utils/Utils';
import {validatePhoneNumber} from '../../utils/validate';
import {
  FAddressPickerForm,
  TextFieldForm,
} from '../Default/form/component-form';
import {useForm} from 'react-hook-form';
import GoogleLogin, {
  webClientId,
} from '../../features/socials-login/GoogleSignIn/GoogleSignIn';
import {CustomerActions} from '../../redux/reducers/CustomerReducer';
import WScreenFooter from '../../Screen/Layout/footer';
import {CustomerStatus, StorageContanst} from '../../Config/Contanst';
import {
  confirmCode,
  signInWithPhoneFB,
} from '../../features/otp-loginwFirebase/PhoneSignIn';
import OTPInput from '../../components/input-otp';

export default function LoginScreen() {
  const methods = useForm({shouldFocusError: false});
  const navigation = useNavigation<any>();
  const dispatch = useDispatch<any>();
  const [isSignUp, setSignUp] = useState(false);
  const [loading, setLoading] = useState(false);
  const [loadingG, setLoadingG] = useState(false);
  const [bio, setBio] = useState<any>('');
  useEffect(() => {
    getDataToAsyncStorage('Mobile').then(result => {
      methods.setValue('LastMobile', result);
      methods.setValue('Mobile', result);
    });
    getDataToAsyncStorage('spBiometrics').then(rs => {
      if (rs == 'true') {
        getDataToAsyncStorage('Biometrics').then(result => {
          if (result) {
            setBio(result);
          }
        });
      }
    });
  }, []);

  const _loginSocial = async (value: any) => {
    if (value) {
      console.log('Google login success', value);
      setLoading(true);
      const res = await CustomerActions.login({
        type: 'google',
        ggClientId: webClientId,
        token: value.idToken,
      });
      if (res.code === 200) {
        await saveDataToAsyncStorage(
          StorageContanst.accessToken,
          res.data.accessToken,
        );
        dispatch(CustomerActions.getInfor());
        showSnackbar({
          message: 'Đăng nhập thành công',
          status: ComponentStatus.SUCCSESS,
        });
        navigateReset(RootScreen.navigateEComView);
        setLoading(false);
      } else {
        showSnackbar({message: res.message, status: ComponentStatus.ERROR});
        setLoading(false);
      }
    }
  };

  return (
    <View
      style={{
        flex: 1,
        paddingTop: 35,
        width: '100%',
        height: Dimensions.get('window').height,
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <FLoading visible={loading} />
      <KeyboardAvoidingView
        style={{
          width: '100%',
          height: '100%',
          paddingHorizontal: 32,
          paddingTop: 32,
        }}>
        <Image
          source={require('../../assets/splash.png')}
          style={{width: 155, height: 155, alignSelf: 'center'}}
        />

        {isSignUp ? (
          <SignUpView methods={methods} />
        ) : (
          <LoginView bio={bio} methods={methods} />
        )}

        <ListTile
          title={
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <Text
                style={[
                  TypoSkin.body3,
                  {color: ColorThemes.light.neutral_text_body_color},
                ]}>
                {isSignUp
                  ? 'Chuyển sang đăng nhập ngay?'
                  : 'Bạn chưa có tài khoản?'}
              </Text>
              <AppButton
                title={isSignUp ? 'Đăng nhập' : 'Đăng ký'}
                textColor={ColorThemes.light.primary_main_color}
                textStyle={{
                  ...TypoSkin.buttonText3,
                  color: ColorThemes.light.primary_main_color,
                  // underline text
                  textDecorationLine: 'underline',
                }}
                containerStyle={{
                  height: 32,
                  borderRadius: 8,
                  paddingHorizontal: 12,
                }}
                borderColor={ColorThemes.light.transparent}
                backgroundColor={ColorThemes.light.transparent}
                onPress={() => {
                  setSignUp(!isSignUp);
                  methods.reset();
                }}
              />
            </View>
          }
          style={{
            paddingVertical: 16,
            padding: 0,
            borderRadius: 0,
            borderTopColor: ColorThemes.light.neutral_main_border_color,
            borderTopWidth: 1,
          }}
          titleStyle={[
            TypoSkin.body3,
            {color: ColorThemes.light.neutral_text_body_color},
          ]}
        />

        {/* social login */}
        {isSignUp ? null : (
          <View style={{paddingTop: 16}}>
            <View
              style={{
                flexDirection: 'row',
                paddingBottom: 16,
                width: '100%',
                justifyContent: 'center',
              }}>
              <Text
                style={[
                  TypoSkin.body3,
                  {
                    alignSelf: 'baseline',
                    color: ColorThemes.light.neutral_text_subtitle_color,
                  },
                ]}>
                Hoặc
              </Text>
            </View>
            <GoogleLogin
              onLoading={setLoadingG}
              isLoading={loadingG}
              onAuthSuccess={_loginSocial}
            />
          </View>
        )}
      </KeyboardAvoidingView>
    </View>
  );
}

// #region Login
const LoginView = ({methods, bio}: {methods: any; bio: string}) => {
  const [isVisiblePass, setVisiblePass] = useState(true);
  const [isForgotPass, setForgotPass] = useState(false);
  const [isOtp, setIsOtp] = useState(false);
  const [loading, setLoading] = useState(false);

  const navigation = useNavigation<any>();
  const dispatch = useDispatch<any>();

  const [allowErrorOpt, setAllowErrorOpt] = useState(0);
  // If null, no SMS has been sent
  const [confirm, setConfirm] = useState<any>(null);

  const validationForm = useMemo(() => {
    if (isOtp) return true;
    return (
      !methods.formState.errors.Mobile?.message &&
      methods.watch('Mobile')?.length > 0 &&
      methods.watch('Password')?.length > 0 &&
      !methods.formState.errors.Password?.message
    );
  }, [
    methods.watch('Mobile'),
    methods.formState.errors.Mobile?.message,
    methods.watch('Password'),
    methods.formState.errors.Password?.message,
    isOtp,
  ]);

  const customerController = new DataController('Customer');

  useEffect(() => {
    let timeoutVariable: string | number | NodeJS.Timeout | undefined;

    if (loading) {
      timeoutVariable = setTimeout(() => setLoading(false), 60000);
    }

    return () => clearTimeout(timeoutVariable);
  }, [loading]);

  const onCheckPhoneForOtp = async () => {
    var mobile = methods.watch('Mobile').trim();
    // Check if the number doesn't already start with 0 or +84
    if (!/^(\+84|0)/.test(mobile)) {
      mobile = '0' + mobile; // Add 0 at the beginning
    }
    if (!validatePhoneNumber(mobile)) {
      methods.setError('Mobile', {message: 'Số điện thoại không hợp lệ'});
      return false;
    }

    setLoading(true);

    const checkLocked = await customerController.getListSimple({
      page: 1,
      size: 1,
      query: `@Mobile:{${mobile}} @Status:[${CustomerStatus.locked} ${CustomerStatus.locked}]`,
    });
    if (checkLocked.data.length) {
      showSnackbar({
        message:
          'Số điện thoại của bạn đã bị khóa, vui lòng liên hệ với quản trị viên để được hỗ trợ.',
        status: ComponentStatus.ERROR,
      });
      setLoading(false);
      return false;
    }
    // var rs = await signInWithPhoneFB(mobile);
    var rs = true;
    if (rs) {
      setIsOtp(true);
      // done
      setConfirm(rs);
      showSnackbar({
        message: 'Đã gửi mã xác thực đến số diện thoại',
        status: ComponentStatus.SUCCSESS,
      });
      setLoading(false);

      return true;
    } else {
      setLoading(false);
      return false;
    }
  };

  const submitOtp = async (value: string) => {
    var mobile = methods.watch('Mobile').trim();
    // Check if the number doesn't already start with 0 or +84
    if (!/^(\+84|0)/.test(mobile)) {
      mobile = '0' + mobile; // Add 0 at the beginning
    }
    if (!confirm) return false;
    setLoading(true);

    // var rsotp = await confirmCode(confirm, value);
    var rsotp = value == '123456';

    if (rsotp === true) {
      console.log('===============otp done=====================');
      setLoading(false);
      setAllowErrorOpt(0);
      return true;
    } else {
      if (allowErrorOpt < 5) {
        setAllowErrorOpt(allowErrorOpt + 1);
      } else {
        const r = await CustomerActions.lockAccount(mobile);
        return showSnackbar({
          message: r.message,
          status: ComponentStatus.ERROR,
        });
      }
      showSnackbar({
        message:
          5 - (allowErrorOpt + 1) == 0
            ? 'Mã xác thực không chính xác'
            : `Mã xác thực không chính xác, bạn còn ${
                5 - (allowErrorOpt + 1)
              } lần nhập lại`,
        status: ComponentStatus.ERROR,
      });
      setLoading(false);
      return false;
    }
  };

  const _loginAction = async (otpValue: string) => {
    var mobile = methods.watch('Mobile')?.trim();
    var password = methods.watch('Password')?.trim();
    // Check if the number doesn't already start with 0 or +84
    if (!/^(\+84|0)/.test(mobile)) {
      mobile = '0' + mobile; // Add 0 at the beginning
    }

    const val = validatePhoneNumber(mobile);
    if (!val) {
      methods.setError('Mobile', {message: 'Số điện thoại không hợp lệ'});
      return;
    }

    if (isOtp && otpValue == '') {
      setIsOtp(false);
      return;
    }
    setLoading(true);
    // using password
    if (!isOtp && otpValue == '') {
      // check sdt da dang ky
      const resCustomers = await customerController.aggregateList({
        page: 1,
        size: 1,
        searchRaw: `@Mobile:(${mobile})`,
      });

      if (resCustomers && resCustomers?.data?.length == 0) {
        showSnackbar({
          message: 'Số điện thoại chưa được đăng ký.',
          status: ComponentStatus.ERROR,
        });
        setLoading(false);
        return;
      }
      if (!password) return;
      if (password === undefined || password.length == 0) {
        methods.setError('Password', {message: 'Mật khẩu không được để trống'});
        return;
      }
      const valPass = regexPassWord.test(password);
      if (!valPass) {
        methods.setError('Password', {
          message: 'Mật khẩu sai định dạng, hãy thử lại',
        });
        return;
      }
      methods.clearErrors('Password');
      setVisiblePass(true);

      const res = await CustomerActions.login({
        type: 'account',
        phone: mobile,
        password: password,
      });
      switch (res.code) {
        case 403:
          methods.setError('Password', {
            message: 'Mật khẩu không đúng, vui lòng kiểm tra lại.',
          });
          showSnackbar({
            message: 'Mật khẩu không đúng, vui lòng kiểm tra lại.',
            status: ComponentStatus.ERROR,
          });
          break;
        case 200:
          saveDataToAsyncStorage(
            'timeRefresh',
            `${Date.now() / 1000 + 9 * 60}`,
          );
          saveDataToAsyncStorage('accessToken', `${res.data.accessToken}`);
          saveDataToAsyncStorage('refreshToken', `${res.data.refreshToken}`);
          saveDataToAsyncStorage('Mobile', `${mobile}`);
          dispatch(CustomerActions.getInfor()).then(() => {
            setLoading(false);
            showSnackbar({
              message: 'Đăng nhập thành công',
              status: ComponentStatus.SUCCSESS,
            });

            navigation.replace(RootScreen.navigateEComView);
          });
          setLoading(false);
          break;
        default:
          break;
      }
      setLoading(false);
    } else {
      // using OTP
      if (isOtp) {
        // check phone
        // submitOtp
        if (otpValue == '' || !confirm) return;
        const rsOtp = await submitOtp(otpValue);
        if (rsOtp === true) {
          const res = await CustomerActions.login({
            type: 'phone',
            phone: mobile,
          });

          if (res.code === 200) {
            saveDataToAsyncStorage(
              'timeRefresh',
              `${Date.now() / 1000 + 9 * 60}`,
            );
            saveDataToAsyncStorage('accessToken', `${res.data.accessToken}`);
            saveDataToAsyncStorage('refreshToken', `${res.data.refreshToken}`);
            saveDataToAsyncStorage('Mobile', `${mobile}`);
            dispatch(CustomerActions.getInfor()).then(() => {
              setLoading(false);
              showSnackbar({
                message: 'Đăng nhập thành công',
                status: ComponentStatus.SUCCSESS,
              });

              navigation.replace(RootScreen.navigateEComView);
            });
            setLoading(false);
          } else {
            showSnackbar({message: res.message, status: ComponentStatus.ERROR});
            setLoading(false);
          }
        }
        setLoading(false);
      }
    }
  };

  const _forgotPassword = async () => {
    var mobile = methods.watch('Mobile')?.trim();
    // Check if the number doesn't already start with 0 or +84
    if (!/^(\+84|0)/.test(mobile) && mobile) {
      mobile = '0' + mobile; // Add 0 at the beginning
    }
    navigation.push(RootScreen.ForgotPass, {isLogin: true, mobile: mobile});
  };

  return (
    <View
      pointerEvents={loading ? 'none' : 'auto'}
      style={{
        width: '100%',
      }}>
      <FLoading visible={loading} avt={require('../../assets/appstore.png')} />
      {isOtp ? (
        <OTPInput
          autoFocus={false}
          disabled={allowErrorOpt > 5}
          length={6}
          onReSendOtp={() => {
            onCheckPhoneForOtp();
          }}
          onSubmit={_loginAction}
        />
      ) : (
        <KeyboardAvoidingView style={{width: '100%', gap: 24}}>
          <TextFieldForm
            control={methods.control}
            name="Mobile"
            placeholder="Nhập số điện thoại của bạn"
            label="Số điện thoại"
            returnKeyType="done"
            errors={methods.formState.errors}
            textFieldStyle={{
              height: 48,
              paddingLeft: 8,
              backgroundColor: ColorThemes.light.transparent,
            }}
            register={methods.register}
            prefix={
              <View
                style={{
                  flexDirection: 'row',
                  height: 32,
                  width: 32,
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: ColorThemes.light.primary_main_color,
                  borderRadius: 100,
                }}>
                <Winicon
                  src="fill/users/contact"
                  size={12}
                  color={ColorThemes.light.neutral_absolute_background_color}
                />
              </View>
            }
            type="number-pad"
            onBlur={async (ev: string) => {
              if (ev === undefined || ev.length == 0) {
                methods.setError('Mobile', {
                  message: 'Số điện thoại không hợp lệ',
                });
                return;
              }
              var mobile = ev.trim();
              // Check if the number doesn't already start with 0 or +84
              if (!/^(\+84|0)/.test(mobile)) {
                mobile = '0' + mobile; // Add 0 at the beginning
              }
              const val = validatePhoneNumber(mobile);
              if (val) methods.clearErrors('Mobile');
              else
                methods.setError('Mobile', {
                  message: 'Số điện thoại không hợp lệ',
                });
            }}
          />
          <TextFieldForm
            control={methods.control}
            name="Password"
            label="Mật khẩu"
            prefix={
              <View
                style={{
                  flexDirection: 'row',
                  height: 32,
                  width: 32,
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: ColorThemes.light.primary_main_color,
                  borderRadius: 100,
                }}>
                <Winicon
                  src="fill/user interface/password"
                  size={12}
                  color={ColorThemes.light.neutral_absolute_background_color}
                />
              </View>
            }
            returnKeyType="done"
            placeholder={'Nhập mật khẩu của bạn'}
            errors={methods.formState.errors}
            secureTextEntry={isVisiblePass}
            textFieldStyle={{
              height: 48,
              backgroundColor: ColorThemes.light.transparent,
              paddingVertical: 16,
              paddingLeft: 8,
            }}
            register={methods.register}
            suffix={
              <TouchableOpacity
                style={{padding: 12}}
                onPress={() => {
                  setVisiblePass(!isVisiblePass);
                }}>
                <Winicon
                  src={
                    isVisiblePass
                      ? `outline/user interface/view`
                      : `outline/user interface/hide`
                  }
                  size={14}
                />
              </TouchableOpacity>
            }
            onBlur={async (ev: string) => {
              var pass = ev.trim();
              if (!regexPassWord.test(pass))
                return methods.setError('Password', {
                  message: 'Mật khẩu sai định dạng, hãy thử lại',
                });
              methods.clearErrors('Password');
            }}
          />
        </KeyboardAvoidingView>
      )}
      {isOtp ? null : (
        <View
          style={{
            paddingTop: methods.formState.errors.Password ? 12 : 0,
            flexDirection: 'row',
            width: '100%',
            justifyContent: 'space-between',
          }}>
          <TouchableOpacity
            onPress={_forgotPassword}
            style={{paddingVertical: 8, alignItems: 'flex-start'}}>
            <Text
              style={[
                TypoSkin.body3,
                {
                  alignSelf: 'baseline',
                  color: ColorThemes.light.neutral_text_subtitle_color,
                },
              ]}>
              Quên mật khẩu?
            </Text>
          </TouchableOpacity>
          <View />
          <TouchableOpacity
            onPress={async () => {
              var mobile = methods.watch('Mobile')?.trim();
              // Check if the number doesn't already start with 0 or +84
              if (!/^(\+84|0)/.test(mobile)) {
                mobile = '0' + mobile; // Add 0 at the beginning
              }

              const val = validatePhoneNumber(mobile);
              if (!val) {
                methods.setError('Mobile', {
                  message: 'Số điện thoại không hợp lệ',
                });
                return;
              }
              // check sdt da dang ky
              const resCustomers = await customerController.aggregateList({
                page: 1,
                size: 1,
                searchRaw: `@Mobile:(${mobile})`,
              });

              if (resCustomers && resCustomers?.data?.length == 0) {
                showSnackbar({
                  message: 'Số điện thoại chưa được đăng ký.',
                  status: ComponentStatus.ERROR,
                });
                setLoading(false);
                return;
              }
              onCheckPhoneForOtp();
            }}
            style={{paddingVertical: 8, alignItems: 'flex-start'}}>
            <Text
              style={[
                TypoSkin.body3,
                {
                  alignSelf: 'baseline',
                  color: ColorThemes.light.primary_main_color,
                },
              ]}>
              Đăng nhập với OTP
            </Text>
          </TouchableOpacity>
        </View>
      )}

      <View
        style={{
          flexDirection: 'row',
          width: '100%',
          gap: 8,
          alignItems: 'center',
        }}>
        <AppButton
          title={isOtp ? 'Thử số khác' : 'Đăng nhập'}
          textColor={ColorThemes.light.neutral_absolute_background_color}
          textStyle={{...TypoSkin.buttonText1, color: ColorThemes.light.white}}
          disabled={!validationForm}
          containerStyle={{height: 48, flex: 1, borderRadius: 8, marginTop: 8}}
          borderColor={ColorThemes.light.neutral_main_border_color}
          backgroundColor={ColorThemes.light.primary_main_color}
          onPress={
            isOtp
              ? () => {
                  setIsOtp(false);
                }
              : () => {
                  _loginAction('');
                }
          }
        />
        {bio == 'true' && !isOtp ? (
          <View style={{paddingTop: 8}}>
            <LocalAuthen
              isFirstTime={
                methods.watch('LastMobile') !== methods.watch('Mobile') &&
                methods.watch('Mobile')?.length != 0
              }
              onSuccess={async value => {
                if (value === true) {
                  setLoading(true);
                  const mobile = await getDataToAsyncStorage('Mobile');

                  const res = await CustomerActions.login({
                    type: 'phone',
                    phone: mobile || methods.watch('Mobile'),
                  });
                  if (res.code === 200) {
                    saveDataToAsyncStorage(
                      'timeRefresh',
                      `${Date.now() / 1000 + 9 * 60}`,
                    );
                    saveDataToAsyncStorage(
                      'accessToken',
                      `${res.data.accessToken}`,
                    );
                    saveDataToAsyncStorage(
                      'refreshToken',
                      `${res.data.refreshToken}`,
                    );
                    dispatch(CustomerActions.getInfor()).then(() => {
                      setLoading(false);
                      showSnackbar({
                        message: 'Đăng nhập thành công',
                        status: ComponentStatus.SUCCSESS,
                      });
                      navigation.replace(RootScreen.navigateEComView);
                    });
                  }
                }
              }}
            />
          </View>
        ) : null}
      </View>
    </View>
  );
};

// #region SignUp
const SignUpView = ({methods}: {methods: any}) => {
  const [isVisiblePass, setVisiblePass] = useState(true);
  const [isVisiblePass2, setVisiblePass2] = useState(true);
  const [isOtp, setIsOtp] = useState(false);
  const [loading, setLoading] = useState(false);
  const dialogRef = useRef<any>(null);
  const navigation = useNavigation<any>();
  const dispatch = useDispatch<any>();

  const [allowErrorOpt, setAllowErrorOpt] = useState(0);
  // If null, no SMS has been sent
  const [confirm, setConfirm] = useState<any>(null);

  const validationForm = useMemo(() => {
    if (isOtp) return true;
    return (
      !methods.formState.errors.Mobile?.message &&
      methods.watch('Mobile')?.length > 0 &&
      methods.watch('Password')?.length > 0 &&
      !methods.formState.errors.Password?.message &&
      methods.watch('ConfirmPassword')?.length > 0 &&
      !methods.formState.errors.ConfirmPassword?.message
    );
  }, [
    methods.watch('Mobile'),
    methods.formState.errors.Mobile?.message,
    methods.watch('Password'),
    methods.formState.errors.Password?.message,
    methods.watch('ConfirmPassword'),
    methods.formState.errors.ConfirmPassword?.message,
    isOtp,
  ]);

  useEffect(() => {
    methods.setValue('Mobile', undefined);
    setLoading(false);
  }, []);

  useEffect(() => {
    let timeoutVariable: string | number | NodeJS.Timeout | undefined;

    if (loading) {
      timeoutVariable = setTimeout(() => setLoading(false), 60000);
    }

    return () => clearTimeout(timeoutVariable);
  }, [loading]);

  const onCheckPhoneForOtp = async () => {
    var mobile = methods.watch('Mobile').trim();
    // Check if the number doesn't already start with 0 or +84
    if (!/^(\+84|0)/.test(mobile)) {
      mobile = '0' + mobile; // Add 0 at the beginning
    }
    if (!validatePhoneNumber(mobile)) {
      methods.setError('Mobile', {message: 'Số điện thoại không hợp lệ'});
      return false;
    }

    setLoading(true);

    // check sdt bi khoa
    // check sdt da dang ky
    const resCustomers = await customerController.aggregateList({
      page: 1,
      size: 1,
      searchRaw: `@Mobile:(${mobile})`,
    });
    if (resCustomers) {
      if (resCustomers?.data?.length > 0) {
        // check sdt bi khoa
        if (resCustomers?.data[0]?.Status == CustomerStatus.locked) {
          showSnackbar({
            message:
              'Số điện thoại của bạn đã bị khóa, vui lòng liên hệ với quản trị viên để được hỗ trợ.',
            status: ComponentStatus.ERROR,
          });
          setLoading(false);
          return false;
        }
        showSnackbar({
          message: 'Số điện thoại đã đăng ký trước đó',
          status: ComponentStatus.ERROR,
        });
        setLoading(false);
        return;
      }
    } else {
      showSnackbar({
        message: 'Đã có lỗi xảy ra',
        status: ComponentStatus.ERROR,
      });
      setLoading(false);
      return;
    }
    // var rs = await signInWithPhoneFB(mobile);
    var rs = true;

    if (rs) {
      // done
      setConfirm(rs);
      showSnackbar({
        message: 'Đã gửi mã xác thực đến số diện thoại',
        status: ComponentStatus.SUCCSESS,
      });
      setLoading(false);
      scrollRef.current?.scrollTo({
        y: 0,
        animated: true,
      });
      return true;
    } else {
      setLoading(false);
      return false;
    }
  };

  const submitOtp = async (value: string) => {
    var mobile = methods.watch('Mobile').trim();
    // Check if the number doesn't already start with 0 or +84
    if (!/^(\+84|0)/.test(mobile)) {
      mobile = '0' + mobile; // Add 0 at the beginning
    }
    if (!confirm) return false;
    setLoading(true);

    // var rsotp = await confirmCode(confirm, value);
    var rsotp = value == '123456';

    if (rsotp === true) {
      console.log('===============otp done=====================');
      setLoading(false);
      setAllowErrorOpt(0);
      return true;
    } else {
      if (allowErrorOpt < 5) {
        setAllowErrorOpt(allowErrorOpt + 1);
      } else {
        const r = await CustomerActions.lockAccount(mobile);
        return showSnackbar({
          message: r.message,
          status: ComponentStatus.ERROR,
        });
      }
      showSnackbar({
        message:
          5 - (allowErrorOpt + 1) == 0
            ? 'Mã xác thực không chính xác'
            : `Mã xác thực không chính xác, bạn còn ${
                5 - (allowErrorOpt + 1)
              } lần nhập lại`,
        status: ComponentStatus.ERROR,
      });
      setLoading(false);
      return false;
    }
  };

  const customerController = new DataController('Customer');
  const scrollRef = useRef<any>(null);

  const _signUp = async (otpValue: string) => {
    var mobile = methods.watch('Mobile')?.trim();
    var password = methods.watch('Password')?.trim();
    // Check if the number doesn't already start with 0 or +84
    if (!/^(\+84|0)/.test(mobile)) {
      mobile = '0' + mobile; // Add 0 at the beginning
    }

    const val = validatePhoneNumber(mobile);
    if (!val) {
      methods.setError('Mobile', {message: 'Số điện thoại không hợp lệ'});
      return;
    }

    if (isOtp && otpValue == '') {
      setIsOtp(false);
      return;
    }

    // submitOtp
    if (otpValue == '' || !confirm) return;
    setLoading(true);

    const rsOtp = await submitOtp(otpValue);

    if (rsOtp === true) {
      const deviceToken = await getDataToAsyncStorage('fcmToken');
      const hashPass = await CustomerActions.hashPassword(password);

      if (hashPass.code != 200) {
        setLoading(false);
        showSnackbar({
          message: hashPass.message,
          status: ComponentStatus.ERROR,
        });
        return;
      }
      debugger;

      const newCus = {
        Id: randomGID(),
        Name: methods.watch('Name'),
        Address: methods.watch('Address'),
        RefCode: methods.watch('RefCode'),
        Long: methods.watch('Long'),
        Lat: methods.watch('Lat'),
        DateCreated: Date.now(),
        Mobile: mobile,
        Status: CustomerStatus.active,
        Password: hashPass.data,
        DeviceToken: deviceToken,
      };
      console.log('newCus', newCus);
      const customerRes = await customerController.add([newCus]);

      if (customerRes.code == 200) {
        const res = await CustomerActions.login({
          type: 'phone',
          phone: mobile,
        });

        if (res.code === 200) {
          saveDataToAsyncStorage(
            'timeRefresh',
            `${Date.now() / 1000 + 9 * 60}`,
          );
          saveDataToAsyncStorage('accessToken', `${res.data.accessToken}`);
          saveDataToAsyncStorage('refreshToken', `${res.data.refreshToken}`);
          saveDataToAsyncStorage('Mobile', `${mobile}`);
          dispatch(CustomerActions.getInfor()).then(() => {
            setLoading(false);
            getDataToAsyncStorage('spBiometrics').then(bio => {
              if (bio == 'true') {
                showDialog({
                  ref: dialogRef,
                  status: ComponentStatus.INFOR,
                  title: 'Sinh trắc học để đăng nhập nhanh hơn?',
                  content: (
                    <View
                      style={{
                        width: '100%',
                        gap: 8,
                        justifyContent: 'center',
                        alignItems: 'center',
                      }}>
                      <Text
                        style={{
                          ...TypoSkin.body3,
                          color: ColorThemes.light.neutral_text_subtitle_color,
                          textAlign: 'center',
                        }}>
                        Bạn có muốn sử dụng bảo mật sinh trắc học để đăng nhập
                        nhanh hơn...
                      </Text>
                      <LocalAuthen
                        sizeIcon={52}
                        isFirstTime={false}
                        onSuccess={async value => {
                          if (value === true) {
                            saveDataToAsyncStorage('Biometrics', 'true');
                            showSnackbar({
                              message: 'Đăng nhập thành công',
                              status: ComponentStatus.SUCCSESS,
                            });
                            navigation.replace(RootScreen.navigateEComParent);
                          }
                        }}
                      />
                    </View>
                  ),
                  // disableCancel: true,
                  onSubmit: async () => {
                    saveDataToAsyncStorage('Biometrics', 'false');
                    showSnackbar({
                      message: 'Đăng nhập thành công',
                      status: ComponentStatus.SUCCSESS,
                    });
                    navigation.replace(RootScreen.navigateEComView);
                  },
                  titleSubmit: 'Để sau',
                });
              } else {
                showSnackbar({
                  message: 'Đăng nhập thành công',
                  status: ComponentStatus.SUCCSESS,
                });
                navigation.replace(RootScreen.navigateEComView);
              }
              setLoading(false);
            });
          });
        } else {
          showSnackbar({message: res.message, status: ComponentStatus.ERROR});
          setLoading(false);
        }
      } else {
        showSnackbar({
          message: customerRes.message ?? 'Đã có lỗi xảy ra khi tạo tài khoản.',
          status: ComponentStatus.ERROR,
        });
        setLoading(false);
        return;
      }
    }
    setLoading(false);
  };

  return (
    <ScrollView
      snapToStart={isOtp ? true : undefined}
      showsVerticalScrollIndicator={false}
      style={{width: '100%'}}
      ref={scrollRef}>
      <FLoading visible={loading} avt={require('../../assets/appstore.png')} />
      <FDialog ref={dialogRef} />
      <Pressable>
        <KeyboardAvoidingView
          behavior={'padding'}
          style={{width: '100%', marginTop: 8}}>
          {isOtp ? (
            <OTPInput
              autoFocus={false}
              length={6}
              disabled={allowErrorOpt > 5}
              onReSendOtp={onCheckPhoneForOtp}
              onSubmit={_signUp}
            />
          ) : (
            <View style={{width: '100%', gap: 16}}>
              <TextFieldForm
                control={methods.control}
                name="Name"
                placeholder="Họ và tên"
                label="Họ và tên"
                returnKeyType="done"
                errors={methods.formState.errors}
                textFieldStyle={{
                  height: 48,
                  paddingVertical: 16,
                  paddingLeft: 8,
                  backgroundColor: ColorThemes.light.transparent,
                }}
                register={methods.register}
                type="name-phone-pad"
                onBlur={(ev: string) => {
                  if (ev?.length !== 0) methods.clearErrors('Name');
                  else
                    methods.setError('Name', {
                      message: 'Họ và tên không được để trống',
                    });
                }}
                prefix={
                  <View
                    style={{
                      flexDirection: 'row',
                      height: 32,
                      width: 32,
                      alignItems: 'center',
                      justifyContent: 'center',
                      backgroundColor: ColorThemes.light.primary_main_color,
                      borderRadius: 100,
                    }}>
                    <Winicon
                      src="fill/users/contact"
                      size={12}
                      color={
                        ColorThemes.light.neutral_absolute_background_color
                      }
                    />
                  </View>
                }
              />
              <TextFieldForm
                control={methods.control}
                name="Mobile"
                required
                label="Số điện thoại"
                placeholder="Nhập số điện thoại của bạn"
                returnKeyType="done"
                errors={methods.formState.errors}
                textFieldStyle={{
                  height: 48,
                  paddingLeft: 8,
                  backgroundColor: ColorThemes.light.transparent,
                }}
                register={methods.register}
                prefix={
                  <View
                    style={{
                      flexDirection: 'row',
                      height: 32,
                      width: 32,
                      alignItems: 'center',
                      justifyContent: 'center',
                      backgroundColor: ColorThemes.light.primary_main_color,
                      borderRadius: 100,
                    }}>
                    <Winicon
                      src="fill/user interface/phone"
                      size={12}
                      color={
                        ColorThemes.light.neutral_absolute_background_color
                      }
                    />
                  </View>
                }
                type="number-pad"
                onBlur={async (ev: string) => {
                  if (ev === undefined || ev.length == 0) {
                    methods.setError('Mobile', {
                      message: 'Số điện thoại không hợp lệ',
                    });
                    return;
                  }
                  var mobile = ev.trim();
                  // Check if the number doesn't already start with 0 or +84
                  if (!/^(\+84|0)/.test(mobile)) {
                    mobile = '0' + mobile; // Add 0 at the beginning
                  }
                  const val = validatePhoneNumber(mobile);
                  if (val) methods.clearErrors('Mobile');
                  else
                    methods.setError('Mobile', {
                      message: 'Số điện thoại không hợp lệ',
                    });
                }}
              />
              <View style={{width: '100%', gap: 8}}>
                <TextFieldForm
                  control={methods.control}
                  name="Password"
                  required
                  secureTextEntry={isVisiblePass}
                  label="Mật khẩu"
                  returnKeyType="done"
                  prefix={
                    <View
                      style={{
                        flexDirection: 'row',
                        height: 32,
                        width: 32,
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: ColorThemes.light.primary_main_color,
                        borderRadius: 100,
                      }}>
                      <Winicon
                        src="fill/user interface/password"
                        size={12}
                        color={
                          ColorThemes.light.neutral_absolute_background_color
                        }
                      />
                    </View>
                  }
                  placeholder={'Tạo mật khẩu của bạn'}
                  suffix={
                    <TouchableOpacity
                      style={{padding: 12}}
                      onPress={() => {
                        setVisiblePass(!isVisiblePass);
                      }}>
                      <Winicon
                        src={
                          isVisiblePass
                            ? `outline/user interface/view`
                            : `outline/user interface/hide`
                        }
                        size={14}
                      />
                    </TouchableOpacity>
                  }
                  errors={methods.formState.errors}
                  textFieldStyle={{
                    height: 48,
                    backgroundColor: ColorThemes.light.transparent,
                    paddingLeft: 8,
                    paddingVertical: 16,
                    marginBottom: methods.formState.errors.Password?.message
                      ? 16
                      : 8,
                  }}
                  register={methods.register}
                  onBlur={async (ev: string) => {
                    if ((ev === undefined || ev.length == 0) && !isOtp) {
                      methods.setError('Password', {
                        message: 'Mật khẩu không được để trống',
                      });
                      return;
                    }
                    var pass = ev.trim();
                    if (!regexPassWord.test(pass))
                      return methods.setError('Password', {
                        message: 'Mật khẩu sai định dạng, hãy thử lại',
                      });
                    methods.clearErrors('Password');
                  }}
                />
                <Text
                  style={[
                    TypoSkin.subtitle4,
                    {
                      alignSelf: 'baseline',
                      color: ColorThemes.light.neutral_text_subtitle_color,
                    },
                  ]}>{`- Tối thiểu 8 ký tự/ Tối đa 16 ký tự \n- Gồm chữ hoa, thường và số`}</Text>
                <TextFieldForm
                  control={methods.control}
                  name="ConfirmPassword"
                  required
                  label="Nhập lại mật khẩu"
                  returnKeyType="done"
                  secureTextEntry={isVisiblePass2}
                  suffix={
                    <TouchableOpacity
                      style={{padding: 12}}
                      onPress={() => {
                        setVisiblePass2(!isVisiblePass2);
                      }}>
                      <Winicon
                        src={
                          isVisiblePass2
                            ? `outline/user interface/view`
                            : `outline/user interface/hide`
                        }
                        size={14}
                      />
                    </TouchableOpacity>
                  }
                  prefix={
                    <View
                      style={{
                        flexDirection: 'row',
                        height: 32,
                        width: 32,
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: ColorThemes.light.primary_main_color,
                        borderRadius: 100,
                      }}>
                      <Winicon
                        src="fill/user interface/password"
                        size={12}
                        color={
                          ColorThemes.light.neutral_absolute_background_color
                        }
                      />
                    </View>
                  }
                  placeholder={'Nhập lại mật khẩu của bạn'}
                  errors={methods.formState.errors}
                  textFieldStyle={{
                    height: 48,
                    backgroundColor: ColorThemes.light.transparent,
                    paddingLeft: 8,
                    paddingVertical: 16,
                  }}
                  register={methods.register}
                  onBlur={async (ev: string) => {
                    if ((ev === undefined || ev.length == 0) && !isOtp) {
                      methods.setError('ConfirmPassword', {
                        message: 'Mật khẩu không được để trống',
                      });
                      return;
                    }
                    var rePass = ev.trim();
                    if (methods.watch('Password') !== rePass)
                      return methods.setError('ConfirmPassword', {
                        message: 'Mật khẩu nhập lại không đúng',
                      });
                    methods.clearErrors('ConfirmPassword');
                  }}
                />
              </View>
              <FAddressPickerForm
                control={methods.control}
                errors={methods.formState.errors}
                name="Address"
                label="Địa chỉ"
                prefix={
                  <View
                    style={{
                      flexDirection: 'row',
                      height: 32,
                      width: 32,
                      alignItems: 'center',
                      justifyContent: 'center',
                      backgroundColor: ColorThemes.light.primary_main_color,
                      borderRadius: 100,
                    }}>
                    <Winicon
                      src="fill/user interface/password"
                      size={12}
                      color={
                        ColorThemes.light.neutral_absolute_background_color
                      }
                    />
                  </View>
                }
                placeholder="Nhập địa chỉ của bạn"
                onChange={value => {
                  methods.setValue('Long', value.geometry.location.lng);
                  methods.setValue('Lat', value.geometry.location.lat);
                  methods.setValue('Address', value.formatted_address);
                  return value.formatted_address;
                }}
                textFieldStyle={{
                  paddingLeft: 8,
                  gap: 12,
                  height: 100,
                }}
              />
              <TextFieldForm
                control={methods.control}
                name="RefCode"
                placeholder="Mã giới thiệu (Nếu có)"
                label="Mã giới thiệu (Nếu có)"
                returnKeyType="done"
                errors={methods.formState.errors}
                textFieldStyle={{
                  height: 48,
                  padding: 16,
                  backgroundColor: ColorThemes.light.transparent,
                }}
                register={methods.register}
                type="name-phone-pad"
                onBlur={(ev: string) => {
                  if (ev?.length !== 0) methods.clearErrors('Name');
                  else
                    methods.setError('Name', {
                      message: 'Họ và tên không được để trống',
                    });
                }}
              />
            </View>
          )}
          <View
            style={{
              flexDirection: 'row',
              width: '100%',
              paddingTop: 16,
              paddingBottom: 130,
            }}>
            <AppButton
              title={isOtp ? 'Thử số khác' : 'Đăng ký'}
              textColor={ColorThemes.light.neutral_absolute_background_color}
              textStyle={{
                ...TypoSkin.buttonText1,
                color: ColorThemes.light.neutral_absolute_background_color,
              }}
              disabled={!validationForm || (loading && !isOtp)}
              containerStyle={{
                height: 48,
                flex: 1,
                borderRadius: 8,
                marginTop: 8,
              }}
              borderColor={ColorThemes.light.neutral_main_border_color}
              backgroundColor={ColorThemes.light.primary_main_color}
              onPress={async () => {
                if (isOtp) setIsOtp(false);
                else {
                  if (loading) return;
                  const rs = await onCheckPhoneForOtp();
                  if (rs) {
                    setIsOtp(true);
                  }
                }
              }}
            />
          </View>
        </KeyboardAvoidingView>
      </Pressable>
    </ScrollView>
  );
};
