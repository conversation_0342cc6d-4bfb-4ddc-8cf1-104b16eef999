import { DataController } from "../../base/baseController";

export class OrderDA {
  private orderController: DataController;
  private orderDetailController: DataController;
  private productController: DataController;

  constructor() {
    this.orderController = new DataController('Order');
    this.orderDetailController = new DataController('OrderDetail');
    this.productController = new DataController('Product');
  }

  async createOrder(order: any) {
    const response = await this.orderController.add(order);
    if (response?.code === 200) {
      return response;
    }
    return null;
  }

  async createOrderDetail(orderDetail: any) {
    const response = await this.orderDetailController.add(orderDetail);
    if (response?.code === 200) {
      return response;
    }
    return null;
  }

  async getOrderByCustomerId(CustomerId: string) {
    const response = await this.orderController.getListSimple({
      query: `@CustomerId: {${CustomerId}}`,
    });
    if (response?.code === 200) {
      return response;
    }
    return null;
  }

  async getOrderByOrderId(OrderId: string) {
    const response = await this.orderController.getPatternList({
      query: `@Id: {${OrderId}}`,
      pattern: {
        CustomerId: ['Id', 'Name', 'Mobile', 'Email'],
        ShopId: ['Id', 'Name', 'Avatar'],
        AddressId: ['Id', 'Address'],
      },
    });
    if (response?.code === 200) {
      return response;
    }
    return null;
  }

  async getOrderDetailsByOrderId(OrderId: string) {
    const response = await this.orderDetailController.getPatternList({
      query: `@OrderId: {${OrderId}}`,
      pattern: {
        ProductId: ['Id', 'Name', 'Img', 'Description', 'Price'],
      },
    });
    if (response?.code === 200) {
      return response;
    }
    return null;
  }

  async getOrderWithDetails(OrderId: string) {
    try {
      // Lấy thông tin đơn hàng
      const orderResponse = await this.getOrderByOrderId(OrderId);
      if (orderResponse?.code !== 200 || !orderResponse.data?.length) {
        return null;
      }

      const order = orderResponse.data[0];

      // Lấy chi tiết đơn hàng
      const detailsResponse = await this.getOrderDetailsByOrderId(OrderId);
      if (detailsResponse?.code === 200 && detailsResponse.data?.length) {
        // Lấy thông tin sản phẩm cho mỗi chi tiết đơn hàng
        const details = detailsResponse.data;
        const productIds = details.map(detail => detail.ProductId);

        // Lấy thông tin sản phẩm
        if (productIds.length > 0) {
          const productsQuery = productIds.map(id => `{${id}}`).join(' | ');
          const productsResponse = await this.productController.getListSimple({
            query: `@Id: ${productsQuery}`,
          });

          if (productsResponse?.code === 200 && productsResponse.data?.length) {
            const products = productsResponse.data;

            // Gắn thông tin sản phẩm vào chi tiết đơn hàng
            details.forEach(detail => {
              const product = products.find(p => p.Id === detail.ProductId);
              if (product) {
                detail.Product = product;
              }
            });
          }
        }

        order.details = details;
      }

      return order;
    } catch (error) {
      console.error('Error getting order with details:', error);
      return null;
    }
  }
  //update trạng thái
  async updateStatusOrder(OrderId: string, Status: number) {
    //lấy order
    const order = await this.getOrderByOrderId(OrderId);
    if (order?.code === 200 && order?.data?.length > 0) {
      const response = await this.orderController.edit([
        {...order?.data[0], Status: Status},
      ]);
      if (response?.code === 200) {
        return response;
      }
    }
    return null;
  }
}
