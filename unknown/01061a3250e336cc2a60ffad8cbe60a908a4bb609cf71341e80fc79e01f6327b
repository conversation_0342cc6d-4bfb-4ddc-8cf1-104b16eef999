/* eslint-disable react/no-unstable-nested-components */
/* eslint-disable react-native/no-inline-styles */
import React, {useEffect, useRef, useState} from 'react';
import {Text, FlatList, RefreshControl} from 'react-native';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';
import {
  AppButton,
  FDialog,
  ListTile,
  SkeletonImage,
} from 'wini-mobile-components';
import {SkeletonPlaceCard} from '../../Default/card/defaultImage';
import {CustomerDA} from '../da';
import {useNavigation, useRoute} from '@react-navigation/native';
import TitleWithBackAction from '../../../Screen/Layout/titleWithBackAction';
import {DefaultProduct} from '../../Default/card/defaultProduct';
import {getDataToAsyncStorage} from '../../../utils/AsyncStorage';
import {StorageContanst} from '../../../Config/Contanst';
import {useTranslation} from 'react-i18next';
import {dialogCheckAcc} from '../../../Screen/Layout/mainLayout';
import {View} from 'react-native';
import ConfigAPI from '../../../Config/ConfigAPI';
import {RootScreen} from '../../../router/router';

interface Props {}
export default function Instructors(props: Props) {
  const [data, setData] = useState<Array<any>>([]);
  const [isLoading, setLoading] = useState(false);
  const navigation = useNavigation<any>();
  const [isRefresh, setRefresh] = useState(false);
  const [page, setpage] = useState(1);
  const {t} = useTranslation();
  const dialogRef = useRef<any>(null);
  const route = useRoute();
  const size = 20;
  const customerDA = new CustomerDA();
  useEffect(() => {
    getData();
  }, []);
  const getData = async () => {
    const result = await customerDA.getBestInstructor(page, size);
    if (result) {
      const cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
      const lst2 = await Promise.all(
        result.data.map(async (item: any) => {
          const customer = await customerDA.getFollowCustomer(item.Id); // Gọi API lấy 2 field
          if (customer) {
            const isfollow = customer.follower.some(
              (a: any) => a.CustomerId === cusId,
            );
            return {
              ...item,
              Img: item.AvatarUrl,
              IsFollowed: isfollow,
              Description: `${
                customer.follower?.length ?? 0
              } Người theo dõi * ${
                customer.following?.length ?? 0
              } Đang theo dõi`,
            };
          } else {
            return {
              ...item,
              Img: item.AvatarUrl,
              IsFollowed: false,
            };
          }
        }),
      );
      setData(lst2);
      setLoading(false);
      setRefresh(false);
    }
    setLoading(false);
    setRefresh(false);
  };

  const onRefresh = async () => {
    setRefresh(true);
    setpage(1);
    await getData();
  };

  return (
    <TitleWithBackAction>
      <FDialog ref={dialogRef} />
      <FlatList
        data={data}
        refreshControl={
          <RefreshControl
            refreshing={isRefresh}
            onRefresh={() => {
              onRefresh();
            }}
          />
        }
        ListHeaderComponent={() => (
          <Text
            style={{
              ...TypoSkin.heading5,
              paddingLeft: 16,
              color: ColorThemes.light.neutral_text_title_color,
            }}>
            Instructors
          </Text>
        )}
        contentContainerStyle={{
          gap: 16,
          backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        }}
        renderItem={({item, index}) => {
          return (
            <ListTile
              key={item.Id}
              onPress={() => {
                navigation.push(RootScreen.ProfileCommunity, {
                  Id: item.Id,
                  forEschool: true,
                });
              }}
              style={{padding: 0, paddingLeft: 16, paddingVertical: 4}}
              listtileStyle={{gap: 16}}
              leading={
                <View
                  style={{
                    width: 56,
                    height: 56,
                    borderRadius: 100,
                    overflow: 'hidden',
                  }}>
                  <SkeletonImage
                    key={item?.AvatarUrl}
                    source={{
                      uri: item?.AvatarUrl
                        ? `${ConfigAPI.urlImg + item?.AvatarUrl}`
                        : 'https://placehold.co/48/FFFFFF/000000/png',
                    }}
                    style={{
                      width: '100%',
                      height: '100%',
                      borderRadius: 100,
                      objectFit: 'cover',
                    }}
                    resizeMode="cover"
                  />
                </View>
              }
              title={item.Name}
              trailing={
                <AppButton
                  key={item.Id}
                  backgroundColor={
                    item?.IsFollowed
                      ? 'transparent'
                      : ColorThemes.light.primary_main_color
                  }
                  borderColor={
                    item?.IsFollowed
                      ? ColorThemes.light.primary_main_color
                      : 'transparent'
                  }
                  onPress={async () => {
                    const token = await getDataToAsyncStorage(
                      StorageContanst.accessToken,
                    );
                    if (token) {
                      if (item?.IsFollowed) {
                        const result = await customerDA.unfollow(item.Id);
                        if (result) {
                          const customer = await customerDA.getFollowCustomer(
                            item.Id,
                          );
                          const updatedData = data.map(obj => {
                            if (obj.Id === item.Id) {
                              return {
                                ...obj,
                                IsFollowed: false,
                                Description: `${
                                  customer?.follower?.length ?? 0
                                } Người theo dõi * ${
                                  customer?.following?.length ?? 0
                                } Đang theo dõi`,
                              }; // Merge changes from item
                            }
                            return obj;
                          });
                          setData(updatedData);
                        }
                      } else {
                        const result = await customerDA.follow(item.Id);
                        if (result) {
                          const customer = await customerDA.getFollowCustomer(
                            item.Id,
                          );
                          const updatedData = data.map(obj => {
                            if (obj.Id === item.Id) {
                              return {
                                ...obj,
                                IsFollowed: true,
                                Description: `${
                                  customer?.follower?.length ?? 0
                                } Người theo dõi * ${
                                  customer?.following?.length ?? 0
                                } Đang theo dõi`,
                              }; // Merge changes from item
                            }
                            return obj;
                          });
                          setData(updatedData);
                        }
                      }
                    } else {
                      ///TODO: navigate confirm login
                      dialogCheckAcc(dialogRef);
                    }
                  }}
                  containerStyle={{
                    borderRadius: 100,
                    paddingHorizontal: 8,
                    height: 32,
                    marginRight: 20,
                  }}
                  title={
                    <Text
                      style={{
                        color: item?.IsFollowed
                          ? ColorThemes.light.primary_main_color
                          : ColorThemes.light.neutral_absolute_background_color,
                      }}>
                      {item?.IsFollowed ? t('following') : t('follow')}
                    </Text>
                  }
                />
              }
            />
          );
          return (
            <DefaultProduct
              key={item.Id}
              containerStyle={{paddingLeft: 16}}
              flexDirection="row"
              data={item}
              noDivider
              actionView={
                <View style={{width: '60%'}}>
                  <AppButton
                    key={item.Id}
                    backgroundColor={
                      item?.IsFollowed
                        ? 'transparent'
                        : ColorThemes.light.primary_main_color
                    }
                    borderColor={
                      item?.IsFollowed
                        ? ColorThemes.light.primary_main_color
                        : 'transparent'
                    }
                    onPress={async () => {
                      const token = await getDataToAsyncStorage(
                        StorageContanst.accessToken,
                      );
                      if (token) {
                        if (item?.IsFollowed) {
                          const result = await customerDA.unfollow(item.Id);
                          if (result) {
                            const customer = await customerDA.getFollowCustomer(
                              item.Id,
                            );
                            const updatedData = data.map(obj => {
                              if (obj.Id === item.Id) {
                                return {
                                  ...obj,
                                  IsFollowed: false,
                                  Description: `${
                                    customer?.follower?.length ?? 0
                                  } Người theo dõi * ${
                                    customer?.following?.length ?? 0
                                  } Đang theo dõi`,
                                }; // Merge changes from item
                              }
                              return obj;
                            });
                            setData(updatedData);
                          }
                        } else {
                          const result = await customerDA.follow(item.Id);
                          if (result) {
                            const customer = await customerDA.getFollowCustomer(
                              item.Id,
                            );
                            const updatedData = data.map(obj => {
                              if (obj.Id === item.Id) {
                                return {
                                  ...obj,
                                  IsFollowed: true,
                                  Description: `${
                                    customer?.follower?.length ?? 0
                                  } Người theo dõi * ${
                                    customer?.following?.length ?? 0
                                  } Đang theo dõi`,
                                }; // Merge changes from item
                              }
                              return obj;
                            });
                            setData(updatedData);
                          }
                        }
                      } else {
                        ///TODO: navigate confirm login
                        dialogCheckAcc(dialogRef);
                      }
                    }}
                    containerStyle={{
                      borderRadius: 100,
                      paddingHorizontal: 8,
                      height: 32,
                      marginRight: 20,
                    }}
                    title={
                      <Text
                        style={{
                          color: item?.IsFollowed
                            ? ColorThemes.light.primary_main_color
                            : ColorThemes.light
                                .neutral_absolute_background_color,
                        }}>
                        {item?.IsFollowed ? t('following') : t('follow')}
                      </Text>
                    }
                  />
                </View>
              }
            />
          );
        }}
        style={{
          width: '100%',
          height: '100%',
          backgroundColor: ColorThemes.light.neutral_absolute_background_color,
        }}
        keyExtractor={item => item.Id?.toString()}
        ListEmptyComponent={() => {
          if (isLoading) {
            return <SkeletonPlaceCard />;
          }
        }}
      />
    </TitleWithBackAction>
  );
}
