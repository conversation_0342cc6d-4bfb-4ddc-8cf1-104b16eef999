import {useSelector, useDispatch} from 'react-redux';
import {AppDispatch, RootState} from '../store/store';
import {
  updateQuantity,
  removeFromCart,
  toggleSelectItem,
  selectStoreItems,
  removeItemsByIds,
  clearCart,
} from '../reducers/CartReducer';
import {CartItem} from '../types/cartTypes';

// Hook để lấy state của giỏ hàng
export const useCartState = () => useSelector((state: RootState) => state.cart);

// Hook để lấy các actions của giỏ hàng
export const useCartActions = () => {
  // const dispatch = useDispatch();
  const dispatch: AppDispatch = useDispatch();
  return {
    // Cập nhật số lượng sản phẩm
    updateItemQuantity: (id: string, quantity: number) => {
      dispatch(updateQuantity({id, quantity}));
    },

    // Xóa sản phẩm khỏi giỏ hàng
    removeItem: (id: string) => {
      dispatch(removeFromCart(id));
    },

    // Chọn/bỏ chọn sản phẩm
    toggleItemSelection: (id: string) => {
      dispatch(toggleSelectItem(id));
    },

    // Chọn/bỏ chọn tất cả sản phẩm của một cửa hàng
    toggleStoreSelection: (storeId: string, selected: boolean) => {
      dispatch(selectStoreItems({storeId, selected}));
    },

    // Xóa các sản phẩm theo danh sách ID
    removeItemsById: (ids: string[]) => {
      dispatch(removeItemsByIds(ids));
    },

    // Xóa tất cả sản phẩm trong giỏ hàng
    clearAllItems: () => {
      dispatch(clearCart());
    },

    // Tính tổng tiền của các sản phẩm đã chọn
    calculateSelectedTotal: (items: CartItem[]) => {
      return items
        .filter(item => item.selected)
        .reduce((total, item) => total + item.Price * item.Quantity, 0);
    },

    // Kiểm tra xem có sản phẩm nào được chọn không
    hasSelectedItems: (items: CartItem[]) => {
      return items.some(item => item.selected);
    },

    // Nhóm các sản phẩm theo cửa hàng
    groupItemsByStore: (items: CartItem[]) => {
      const storeMap = new Map<
        string,
        {
          ShopId: string;
          ShopName: string;
          ShopAvatar: string;
          items: CartItem[];
        }
      >();

      items.forEach(item => {
        if (!storeMap.has(item.ShopId)) {
          storeMap.set(item.ShopId, {
            ShopId: item.ShopId,
            ShopName: item.ShopName,
            ShopAvatar: item.ShopAvatar,
            items: [],
          });
        }

        storeMap.get(item.ShopId)?.items.push(item);
      });

      return Array.from(storeMap.values());
    },
  };
};
