import {
  <PERSON>,
  Text,
  Button,
  Image,
  Alert,
  ActivityIndicator,
  Platform,
} from 'react-native';

import {TouchableOpacity, StyleSheet} from 'react-native';
import {
  User,
  GoogleSignin,
  statusCodes,
} from '@react-native-google-signin/google-signin';
import auth from '@react-native-firebase/auth';
import {TypoSkin} from '../../../assets/skin/typography';
import {AppSvg} from 'wini-mobile-components';
import {ColorThemes} from '../../../assets/skin/colors';

const logoGoogle = `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M12.2446 9.81812V14.4654H18.8347C18.5453 15.9599 17.6769 17.2254 16.3745 18.0763L20.3485 21.0982C22.664 19.0037 23.9998 15.9273 23.9998 12.2728C23.9998 11.4219 23.9219 10.6036 23.7771 9.81825L12.2446 9.81812Z" fill="#4285F4"/>
<path d="M5.38289 14.2841L4.48659 14.9564L1.31396 17.3782C3.32882 21.2945 7.45843 24 12.2451 24C15.5512 24 18.323 22.9309 20.349 21.0982L16.375 18.0764C15.284 18.7964 13.8925 19.2328 12.2451 19.2328C9.0614 19.2328 6.35644 17.1274 5.3879 14.291L5.38289 14.2841Z" fill="#34A853"/>
<path d="M1.31346 6.62181C0.478619 8.23629 0 10.0581 0 11.9999C0 13.9417 0.478619 15.7635 1.31346 17.378C1.31346 17.3888 5.38779 14.2798 5.38779 14.2798C5.14289 13.5598 4.99813 12.7963 4.99813 11.9998C4.99813 11.2033 5.14289 10.4397 5.38779 9.71974L1.31346 6.62181Z" fill="#FBBC05"/>
<path d="M12.2453 4.77817C14.0487 4.77817 15.6517 5.38906 16.9318 6.56726L20.4383 3.13094C18.3121 1.18916 15.5515 0 12.2453 0C7.45868 0 3.32882 2.69454 1.31396 6.62181L5.38816 9.71999C6.35658 6.88361 9.06165 4.77817 12.2453 4.77817Z" fill="#EA4335"/>
</svg>
`;

// google
export const webClientId =
  '632449728851-7ffpkm5q12qaqk5psvr31jblfvgknjvt.apps.googleusercontent.com';
interface Props {
  onAuthSuccess: (value: User) => void;
  onLoading: (value: boolean) => void;
  isLoading: boolean;
}

GoogleSignin.configure({
  offlineAccess: true,
  webClientId: webClientId,
});
const GoogleLogin = (props: Props) => {
  const {onAuthSuccess, onLoading, isLoading} = props;

  const signIn = async () => {
    if (isLoading) {
      return;
    }
    onLoading(true);
    try {
      await GoogleSignin.signOut();
      await GoogleSignin.hasPlayServices({showPlayServicesUpdateDialog: true});
      const userInfo = await GoogleSignin.signIn();
      // console.log("userInfo google: ", userInfo);
      let userProfile = userInfo.data;
      console.log('userProfile google: ', userInfo);

      onAuthSuccess(userProfile as User);
      onLoading(false);
      // Create a Google credential with the token
      const googleCredential = auth.GoogleAuthProvider.credential(
        userProfile?.idToken ?? '',
      );
      // Sign-in the user with the credential
      auth().signInWithCredential(googleCredential);
    } catch (error: any) {
      console.log(error);

      if (error?.code === statusCodes.SIGN_IN_CANCELLED) {
        onLoading(false);
      } else if (error?.code === statusCodes.IN_PROGRESS) {
      } else if (error?.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
        Alert.alert(
          'Thông báo',
          'Dịch vụ hiển tại đang không có sẵn, thử lại sau!',
          [{text: 'OK', onPress: () => {}}],
          {cancelable: false},
        );
      } else {
        // console.log(error?.toString());

        Alert.alert(
          'Thông báo',
          'Error! Try again later!',
          [{text: 'OK', onPress: () => {}}],
          {
            cancelable: false,
          },
        );
      }
      onLoading(false);
    }
  };
  return (
    <TouchableOpacity onPress={signIn} style={styles.TouchStyle}>
      {isLoading ? (
        <View style={styles.loading}>
          <ActivityIndicator size="large" />
        </View>
      ) : (
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          <AppSvg SvgSrc={logoGoogle} size={20} />
          <Text
            style={[
              TypoSkin.buttonText1,
              {
                color: ColorThemes.light.neutral_text_body_color,
                fontSize: 18,
                paddingLeft: 8,
              },
            ]}>
            Đăng nhập với Google
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );
};
const styles = StyleSheet.create({
  TouchStyle: {
    flexDirection: 'row',
    width: '100%',
    height: 44,
    borderRadius: 25,
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_main_border_color,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loading: {
    alignItems: 'center',
    justifyContent: 'center',
  },
});
export default GoogleLogin;
