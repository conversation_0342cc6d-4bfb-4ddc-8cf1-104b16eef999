import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
  Platform,
  Dimensions,
  SafeAreaView,
} from 'react-native';
import { Winicon } from 'wini-mobile-components';
import { useNavigation } from '@react-navigation/native';
import Svg, { Path } from 'react-native-svg';

const { width } = Dimensions.get('window');
const STATUSBAR_HEIGHT = StatusBar.currentHeight || 0;

interface HeaderGroupProps {
  title: string;
  onBack?: () => void;
  rightComponent?: React.ReactNode;
  backgroundColor?: string;
  waveColor?: string;
  textColor?: string;
}

const HeaderGroup: React.FC<HeaderGroupProps> = ({
  title,
  onBack,
  rightComponent,
  backgroundColor = '#2962FF', // Blue color as shown in the image
  waveColor = '#8CDBF4',       // Light blue color for the wave
  textColor = '#FFFFFF',       // White text color
}) => {
  const navigation = useNavigation();
  
  const handleBackPress = () => {
    if (onBack) {
      onBack();
    } else {
      navigation.goBack();
    }
  };

  // Set the status bar to match the header
  React.useEffect(() => {
    StatusBar.setBarStyle('light-content');
    if (Platform.OS === 'android') {
      StatusBar.setBackgroundColor(backgroundColor);
      StatusBar.setTranslucent(true);
    }
    
    return () => {
      // Reset status bar when component unmounts
      StatusBar.setBarStyle('default');
      if (Platform.OS === 'android') {
        StatusBar.setBackgroundColor('transparent');
      }
    };
  }, [backgroundColor]);

  return (
    <View style={[styles.container, { backgroundColor }]}>
      {/* Status bar space */}
      <SafeAreaView style={{ backgroundColor }}>
        <View style={styles.statusBarPlaceholder} />
      </SafeAreaView>
      
      {/* Wave effect at the bottom */}
      <View style={styles.waveContainer}>
        <Svg height="80" width={width} viewBox={`0 0 ${width} 80`}>
          <Path
            d={`
              M0 0
              L${width} 0
              L${width} 40
              C${width * 0.75} 60, ${width * 0.25} 60, 0 40
              Z
            `}
            fill={waveColor}
          />
        </Svg>
      </View>
      
      {/* Header content */}
      <View style={styles.headerContent}>
        <TouchableOpacity onPress={handleBackPress} style={styles.backButton}>
          <Winicon src="outline/arrows/left-arrow" size={24} color={textColor} />
        </TouchableOpacity>
        
        <Text style={[styles.title, { color: textColor }]}>{title}</Text>
        
        {rightComponent && (
          <View style={styles.rightComponent}>
            {rightComponent}
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: 150, // Adjust based on your design
    position: 'relative',
  },
  statusBarPlaceholder: {
    height: Platform.OS === 'ios' ? 0 : STATUSBAR_HEIGHT,
  },
  waveContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 1,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 16,
    height: 56,
    position: 'relative',
    zIndex: 2,
  },
  backButton: {
    position: 'absolute',
    left: 16,
    padding: 8,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    textAlign: 'center',
  },
  rightComponent: {
    position: 'absolute',
    right: 16,
  },
});

export default HeaderGroup;
