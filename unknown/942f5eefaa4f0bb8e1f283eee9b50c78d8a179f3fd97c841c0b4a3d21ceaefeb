import {Text, View} from 'react-native';
import {navigateBack} from '../../../router/router';
import TitleWithBackAction from '../../../Screen/Layout/titleWithBackAction';
import {ListTile, WSwitch} from 'wini-mobile-components';
import {useState, useEffect} from 'react';
import ReactNativeBiometrics from 'react-native-biometrics';
import {
  getDataToAsyncStorage,
  saveDataToAsyncStorage,
} from '../../../utils/AsyncStorage';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';
import {FaceID, TouchID} from '../../../features/local-authen/local-authen';
import {ColorThemes} from '../../../assets/skin/colors';
import {TypoSkin} from '../../../assets/skin/typography';

export default function BiometricSetting() {
  const customer = useSelectorCustomerState().data;
  const [biometric, setBiometric] = useState(false);
  const [biometricType, setBiometricType] = useState<any>();
  const [avaiBiometric, setAvaiBiometric] = useState(false);
  const rnBiometrics = new ReactNativeBiometrics({
    allowDeviceCredentials: true,
  });
  useEffect(() => {
    getDataToAsyncStorage('spBiometrics').then(rs => {
      if (rs == 'true') {
        setAvaiBiometric(true);
        getDataToAsyncStorage('biometryType').then(result => {
          setBiometricType(result);
        });
        getDataToAsyncStorage('Biometrics').then(result => {
          setBiometric(result == 'true' ? true : false);
        });
      } else {
        setAvaiBiometric(false);
        setBiometric(false);
      }
    });
  }, []);
  return (
    <TitleWithBackAction onBack={() => navigateBack()}>
      <View style={{padding: 16}}>
        {avaiBiometric && customer ? (
          <ListTile
            style={{padding: 0}}
            leading={
              <View>
                {(() => {
                  if (biometricType == 'TouchID') {
                    return (
                      <TouchID
                        size={20}
                        color={ColorThemes.light.neutral_text_subtitle_color}
                      />
                    );
                  } else {
                    return (
                      <FaceID
                        size={20}
                        color={ColorThemes.light.neutral_text_subtitle_color}
                      />
                    );
                  }
                })()}
              </View>
            }
            title="Sử dụng sinh trắc học"
            titleStyle={[
              TypoSkin.heading8,
              {color: ColorThemes.light.neutral_text_title_color},
            ]}
            trailing={
              <WSwitch
                color={ColorThemes.light.primary_main_color}
                value={biometric}
                onChange={vl => {
                  rnBiometrics
                    .simplePrompt({promptMessage: 'Xác nhận sinh trắc học'})
                    .then(resultObject => {
                      const {success} = resultObject;
                      if (success) {
                        if (vl) {
                          setTimeout(() => {
                            saveDataToAsyncStorage('Biometrics', 'true');
                            setBiometric(true);
                          }, 100);
                          return;
                        } else {
                          setTimeout(() => {
                            saveDataToAsyncStorage('Biometrics', 'false');
                            setBiometric(false);
                          }, 100);
                        }
                      } else {
                        setTimeout(() => {
                          saveDataToAsyncStorage(
                            'Biometrics',
                            biometric ? 'true' : 'false',
                          );
                          setBiometric(biometric);
                        }, 100);
                        console.log(
                          'user cancelled biometric prompt',
                          biometric,
                        );
                      }
                    })
                    .catch(() => {
                      setTimeout(() => {
                        saveDataToAsyncStorage('Biometrics', 'false');
                        setBiometric(false);
                      }, 100);
                      console.log('biometrics failed', biometric);
                    });
                }}
              />
            }
          />
        ) : null}
      </View>
    </TitleWithBackAction>
  );
}
