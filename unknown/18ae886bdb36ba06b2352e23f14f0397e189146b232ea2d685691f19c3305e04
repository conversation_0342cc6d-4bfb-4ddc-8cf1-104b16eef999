import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  SafeAreaView,
  Animated,
} from 'react-native';
import {ColorThemes} from '../../assets/skin/colors';
import {navigateReset, RootScreen} from '../../router/router';

const IntroPage = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const fadeAnim = new Animated.Value(0);
  const slideAnim = new Animated.Value(0);

  const slides = [
    {
      id: 1,
      title: 'Earn commissions',
      color: '#ffffff',
    },
    {
      id: 2,
      description:
        'The affiliate marketing system helps the store increase revenue and build a marketing team.',
      color: '#ffffff',
    },
    {
      id: 3,
      description:
        'Join the affiliate system to elevate your brand, grow your personal value, and thrive with a professional marketing team.',
      color: '#ffffff',
    },
  ];

  useEffect(() => {
    // Animation khi component mount
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();
  }, [currentSlide]);

  const nextSlide = () => {
    fadeAnim.setValue(0);
    slideAnim.setValue(0);
    if (currentSlide === 2) {
      // vào trang chủ
      navigateReset(RootScreen.login);
      return;
    }
    setCurrentSlide(currentSlide + 1);
  };

  const currentSlideData = slides[currentSlide];

  return (
    <SafeAreaView style={[{paddingTop: 30}]}>
      {currentSlide === 0 && (
        <Animated.View
          style={[
            {
              width: '100%',
              height: '80%',
              justifyContent: 'center',
            },
            {
              opacity: fadeAnim,
              transform: [
                {
                  translateY: slideAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [50, 0],
                  }),
                },
              ],
            },
          ]}>
          <View
            style={{
              position: 'relative',
              marginTop: 100,
              height: 400,
            }}>
            <View style={styles.layer1}>
              <View style={{alignItems: 'center'}}>
                <Image
                  style={{marginBottom: 40}}
                  source={require('../../assets/logo.png')}
                />
              </View>
              <Image
                style={styles.phone}
                source={require('../../assets/images/phone.png')}
              />
            </View>
            <View style={styles.layer2}>
              <Image
                style={styles.sphere}
                source={require('../../assets/images/sphere.png')}
              />
            </View>
            <View style={styles.layer3}>
              <Image
                style={{height: 120, width: 120}}
                source={require('../../assets/images/sphere_green.png')}
              />
            </View>
          </View>
          <View style={styles.textContainer}>
            <Text style={styles.title}>{currentSlideData.title}</Text>
          </View>
        </Animated.View>
      )}
      {currentSlide === 1 && (
        <Animated.View
          style={[
            {width: '100%', height: '80%'},
            {
              opacity: fadeAnim,
              transform: [
                {
                  translateY: slideAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [50, 0],
                  }),
                },
              ],
            },
          ]}>
          <View style={{alignItems: 'center'}}>
            <Image
              style={{marginBottom: 20, height: 200, width: 345}}
              source={require('../../assets/splash.png')}
            />
          </View>
          <View style={{alignItems: 'center'}}>
            <Image source={require('../../assets/images/intro_slide_2.png')} />
          </View>
          <Text style={styles.description}>{currentSlideData.description}</Text>
        </Animated.View>
      )}
      {currentSlide === 2 && (
        <Animated.View
          style={[
            {width: '100%', height: '80%'},
            {
              opacity: fadeAnim,
              transform: [
                {
                  translateY: slideAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [50, 0],
                  }),
                },
              ],
            },
          ]}>
          <View style={{alignItems: 'center'}}>
            <Image
              style={{marginBottom: 20, height: 200, width: 345}}
              source={require('../../assets/splash.png')}
            />
          </View>
          <View style={{alignItems: 'center'}}>
            <Image
              style={{}}
              source={require('../../assets/images/intro_slide_3.png')}
            />
          </View>
          <Text style={styles.description}>{currentSlideData.description}</Text>
        </Animated.View>
      )}

      <View style={styles.bottomSection}>
        <View style={styles.indicators}>
          {slides.map((_, index) => (
            <View
              key={index}
              style={[
                styles.indicator,
                {
                  backgroundColor:
                    index === currentSlide
                      ? ColorThemes.light.primary_main_color
                      : '#D9D9D9',
                },
              ]}
            />
          ))}
        </View>

        <TouchableOpacity style={styles.nextButton} onPress={() => nextSlide()}>
          <Text style={styles.nextButtonText}>
            {currentSlide === slides.length - 1 ? 'Get Started' : 'Continue'}
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  layer1: {
    position: 'absolute',
    top: '40%',
    left: '50%',
    transform: [{translateX: '-50%'}, {translateY: '-50%'}],
    zIndex: 2,
  },
  layer2: {
    position: 'absolute',
    left: 0,
    bottom: 0,
    zIndex: 1,
  },
  layer3: {
    position: 'absolute',
    height: 50,
    width: 50,
    right: 40,
    bottom: 95,
    zIndex: 0,
  },
  icon: {
    fontSize: 80,
    textAlign: 'center',
  },
  textContainer: {
    marginTop: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000000',
    textAlign: 'center',
    marginBottom: 12,
  },
  description: {
    fontSize: 12,
    color: '#000000',
    textAlign: 'center',
    lineHeight: 24,
    paddingHorizontal: 20,
    paddingTop: 10,
  },
  bottomSection: {
    marginTop: 30,
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  indicators: {
    flexDirection: 'row',
    marginVertical: 20,
  },
  indicator: {
    height: 8,
    width: 8,
    borderRadius: 20,
    marginHorizontal: 8,
  },
  nextButton: {
    backgroundColor: '#1C33FF',
    paddingHorizontal: 40,
    paddingVertical: 16,
    borderRadius: 30,
    borderWidth: 2,
    borderColor: '#1C33FF',
    minWidth: 270,
  },
  nextButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
  },
  phone: {
    width: 260,
    height: 370,
  },
  sphere: {
    width: 350,
    height: 380,
  },
});

export default IntroPage;
