import React from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import Svg, { Path } from 'react-native-svg';

const { width } = Dimensions.get('window');

interface TabBarBackgroundProps {
  color?: string;
  borderColor?: string;
}

const TabBarBackground: React.FC<TabBarBackgroundProps> = ({
  color = '#FFFFFF',
  borderColor = '#E0E0E0',
}) => {
  // Tính toán kích thước và vị trí
  const tabBarWidth = width - 20; // Trừ đi padding 10px mỗi bên
  const height = 60;
  const curveHeight = 15;
  const curveWidth = 80;
  const curveRadius = 20;
  
  // Vị trí của đường cong ở giữa
  const curveStart = (tabBarWidth - curveWidth) / 2;
  const curveEnd = curveStart + curveWidth;

  // Tạo đường dẫn SVG cho hình dạng tab bar với đường cong ở giữa
  const createPath = () => {
    // Bắt đầu từ góc trên bên trái
    let path = `M 0,${curveRadius}`;
    
    // Vẽ đường cong ở góc trên bên trái
    path += ` Q 0,0 ${curveRadius},0`;
    
    // Vẽ đường thẳng đến điểm bắt đầu của đường cong ở giữa
    path += ` L ${curveStart},0`;
    
    // Vẽ đường cong lên trên ở giữa (phần bên trái)
    path += ` Q ${curveStart + curveWidth/4},0 ${curveStart + curveWidth/4},-${curveHeight}`;
    
    // Vẽ đường cong xuống (phần giữa)
    path += ` Q ${curveStart + curveWidth/2},-${curveHeight + 5} ${curveStart + curveWidth*3/4},-${curveHeight}`;
    
    // Vẽ đường cong xuống dưới (phần bên phải)
    path += ` Q ${curveEnd},0 ${curveEnd},0`;
    
    // Vẽ đường thẳng đến góc trên bên phải
    path += ` L ${tabBarWidth - curveRadius},0`;
    
    // Vẽ đường cong ở góc trên bên phải
    path += ` Q ${tabBarWidth},0 ${tabBarWidth},${curveRadius}`;
    
    // Vẽ đường thẳng xuống góc dưới bên phải
    path += ` L ${tabBarWidth},${height}`;
    
    // Vẽ đường thẳng sang góc dưới bên trái
    path += ` L 0,${height}`;
    
    // Đóng đường dẫn
    path += ' Z';
    
    return path;
  };

  return (
    <View style={styles.container}>
      <Svg width={tabBarWidth} height={height + curveHeight} style={styles.svg}>
        <Path
          d={createPath()}
          fill={color}
          stroke={borderColor}
          strokeWidth={1}
        />
      </Svg>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 0,
    left: 10,
    right: 10,
    height: 60,
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  svg: {
    position: 'absolute',
    bottom: 0,
  },
});

export default TabBarBackground;
